{"time":"2025-08-03T01:11:17.121043+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-03T01:11:17.126366+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-03T01:11:17.647488+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-03T01:11:18.809408+03:00","level":"INFO","msg":"OK   20250424200609_initial.sql (2.31ms)"}
{"time":"2025-08-03T01:11:18.81012+03:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (656.04µs)"}
{"time":"2025-08-03T01:11:18.810597+03:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (444.67µs)"}
{"time":"2025-08-03T01:11:18.811117+03:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (498.67µs)"}
{"time":"2025-08-03T01:11:18.811131+03:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-03T01:11:18.811533+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-03T01:11:18.847289+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-03T01:11:18.84752+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"supabase"}
{"time":"2025-08-03T01:11:18.847556+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"shell-command"}
{"time":"2025-08-03T01:11:18.847524+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"nodejs-debugger"}
{"time":"2025-08-03T01:11:18.847615+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"ccusage"}
{"time":"2025-08-03T01:11:18.847581+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"github.com/NightTrek/Software-planning-mcp"}
{"time":"2025-08-03T01:11:18.847576+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"ios-simulator"}
{"time":"2025-08-03T01:11:18.847589+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"@21st-dev/magic"}
{"time":"2025-08-03T01:11:18.847663+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"Sentry"}
{"time":"2025-08-03T01:11:18.847668+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"Figma"}
{"time":"2025-08-03T01:11:18.847648+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"TestSprite"}
{"time":"2025-08-03T01:11:18.847587+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"sequential-thinking"}
{"time":"2025-08-03T01:11:18.847603+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"XcodeBuildMCP"}
{"time":"2025-08-03T01:11:18.847543+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"playwright"}
{"time":"2025-08-03T01:11:18.847599+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"memory"}
{"time":"2025-08-03T01:11:18.847581+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"apple-doc-mcp"}
{"time":"2025-08-03T01:11:18.84763+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"basic-memory"}
{"time":"2025-08-03T01:11:18.847639+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"context7"}
{"time":"2025-08-03T01:11:18.847635+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"github.com/Dhravya/apple-mcp"}
{"time":"2025-08-03T01:11:18.847645+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"github"}
{"time":"2025-08-03T01:11:18.847616+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"github.com/AgentDeskAI/browser-tools-mcp"}
{"time":"2025-08-03T01:11:18.847655+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"openai-gpt-image-mcp"}
{"time":"2025-08-03T01:11:18.847656+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"gitmcp"}
{"time":"2025-08-03T01:11:18.847662+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"Framelink Figma MCP"}
{"time":"2025-08-03T01:11:18.84759+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"desktop-commander"}
{"time":"2025-08-03T01:11:18.847633+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"TalkToFigma"}
{"time":"2025-08-03T01:11:18.84765+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"browsermcp"}
{"time":"2025-08-03T01:11:18.847669+03:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":158},"msg":"error creating mcp client","error":"unsupported mcp type: ","name":"mcp-supermemory-ai"}
{"time":"2025-08-03T01:11:18.848067+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-03T01:11:18.886142+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-03T01:11:18.886194+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-03T01:11:21.637073+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-03T01:11:21.63715+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-03T01:11:21.673681+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-03T01:11:21.673757+03:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
