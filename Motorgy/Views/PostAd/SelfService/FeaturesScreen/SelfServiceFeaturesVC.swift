//
//  SelfServiceFeaturesVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 26/03/2024.
//  Copyright © 2024 Bo<PERSON>z. All rights reserved.
//

import UIKit
import FirebaseAnalytics
import SwiftUI

class SelfServiceFeaturesVC: BaseVC {
    @IBOutlet weak var titleLabel: labelLocalization!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var tagsListView: SSHorizontalTagsListView!
    private let additionalNotesView = AdditionalNotesCustomView()
    @IBOutlet weak var buttonsContainerView: UIView!
    @IBOutlet weak var skipButton: buttonLocalization!
    @IBOutlet weak var continueButton: buttonLocalization!
    @IBOutlet weak var questionLabelTopConstraint: NSLayoutConstraint!
    @IBOutlet weak var tagsViewHeightConstraint: NSLayoutConstraint!
    
    private var uploadedImages: [UploadImageItemModel]?
    private var selfSellingUserData: [String: Any]?
    private var userSelectedNewData: [Int: Any]?
    private var lstExtraOptionss: [LstExtraOptionss] = []
    private var editableLstExtraOptionss: [LstExtraOptionss] = []
    private var selectedFeaturesIds: [Int] = []
    private var selectedPakcage: LstPackages?
    private var lstPackages: [LstPackages] = []
    private var carDetailsViewModel = CarDetailsViewModel()
    private var isTrim = false
    private var additionalNotes: String = ""
    private var isOpenFromEditDashboard = false
    private weak var delegate: EditSelfServiceCarDetailsProtocol?
    private weak var checkoutDelegate: EditSelfServiceCarDetailsProtocol?
    private var openFromEditAfterCheckout: Bool = false
    private var numberOfDisplayedItems: Int = 7
    private var isWantMotorgyOfferSelfService: Bool = false
    private var myCarDetailsViewModel = MyCarDetailsViewModel()
	private var carListingConfirmationViewModel: CarListingConfirmationViewModel?
    
    private enum SelfServiceFeaturesVCSections: Int, CaseIterable {
        case features = 0
        case divider = 1
        case featuresAdditionalNote = 2
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.navigationItem.titleView = self.createCustomTitleView()
        
        self.titleLabel.text = "Select your car features".localized
        self.titleLabel.textColor = Colors.charcoalColor
        self.titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 16 : 18)
        
        self.tagsListView.isHidden = self.isOpenFromEditDashboard || self.openFromEditAfterCheckout
        self.tagsListView.configureViewForSelfSellingSteps(.additionalInformation, tagListType: .selfServiceSteps, currentSelectedPackageId: self.selectedPakcage?.packageId ?? 0)
        
        DispatchQueue.main.async {
            self.questionLabelTopConstraint.constant = self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ? 24 : 60
            self.tagsViewHeightConstraint.constant = self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ? 0 : 56
        }
        
        self.buttonsContainerView.addTopShadow(
            shadowColor: Colors.topShadowBorderColor,
            shadowOpacity: 1,
            shadowRadius: 20,
            offset: CGSize(width: 0, height: 4)
        )
        
        self.continueButton.setTitle("Continue".localized, for: .normal)
        self.continueButton.addTarget(self, action: #selector(continueAction), for: .touchUpInside)
        
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            self.continueButton.isHidden = false
        } else {
            self.continueButton.isHidden = self.selectedFeaturesIds.isEmpty
        }
        
        self.skipButton.setTitle("Skip".localized, for: .normal)
        self.skipButton.addTarget(self, action: #selector(skipAction), for: .touchUpInside)
        
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.showsVerticalScrollIndicator = false
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(SelfServiceEnhancedFeaturesTVC.self, forCellReuseIdentifier: "SelfServiceEnhancedFeaturesTVC")
        tableView.register(FeaturesDividerCell.nib(), forCellReuseIdentifier: FeaturesDividerCell.identifier)
        tableView.register(BodyPartAdditionalNotesTVC.nib(), forCellReuseIdentifier: BodyPartAdditionalNotesTVC.identifier)
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 16, right: 0)
        
        self.setupDataSource()
        
        self.addNavigationButton()
    }
    
    private func setupDataSource() {
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            if let lstExtraOptionss = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.features.rawValue) as? [LstExtraOptionss] {
                self.editableLstExtraOptionss = lstExtraOptionss
                
                self.editableLstExtraOptionss = self.editableLstExtraOptionss.enumerated().map { index, group in
                    var mutableGroup = group
                    var options = Array(mutableGroup.extraOptions?.prefix(self.numberOfDisplayedItems) ?? [])
                    
                    mutableGroup.extraOptions = mutableGroup.extraOptions?.map { option in
                        var mutableOption = option
                        if selectedFeaturesIds.contains(option.id ?? -1) {
                            mutableOption.isSelected = true
                        }
                        return mutableOption
                    }
                    
                    options = options.map { option in
                        var mutableOption = option
                        if selectedFeaturesIds.contains(option.id ?? -1) {
                            mutableOption.isSelected = true
                        }
                        return mutableOption
                    }
                    
                    if (mutableGroup.extraOptions?.count ?? 0) >= self.numberOfDisplayedItems {
                        let moreOption = ExtraOptions(id: -1, name: "More".localized)
                        options.append(moreOption)
                    }
                    
                    mutableGroup.displayOptions = options
                    
                    let height = self.calculateHeightForSubArrayInsideEachGroup(options)
                    mutableGroup.subArrayHeight = height
                    
                    if index == 0 {
                        mutableGroup.isExpanded = true
                    }
                    
                    return mutableGroup
                }
                
                self.tableView.reloadData()
            }
        } else {
            if let lstExtraOptionss = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.features.rawValue) as? [LstExtraOptionss] {
                self.lstExtraOptionss = self.lstExtraOptionss.isEmpty ? lstExtraOptionss : self.lstExtraOptionss
                
                self.lstExtraOptionss = self.lstExtraOptionss.enumerated().map { index, group in
                    var mutableGroup = group
                    var options = Array(mutableGroup.extraOptions?.prefix(self.numberOfDisplayedItems) ?? [])
                    
                    mutableGroup.extraOptions = mutableGroup.extraOptions?.map { option in
                        var mutableOption = option
                        if selectedFeaturesIds.contains(option.id ?? -1) {
                            mutableOption.isSelected = true
                        }
                        return mutableOption
                    }
                    
                    options = options.map { option in
                        var mutableOption = option
                        if selectedFeaturesIds.contains(option.id ?? -1) {
                            mutableOption.isSelected = true
                        }
                        return mutableOption
                    }
                    
                    if (mutableGroup.extraOptions?.count ?? 0) >= self.numberOfDisplayedItems {
                        let moreOption = ExtraOptions(id: -1, name: "More".localized)
                        options.append(moreOption)
                    }
                    
                    mutableGroup.displayOptions = options
                    
                    let height = self.calculateHeightForSubArrayInsideEachGroup(options)
                    mutableGroup.subArrayHeight = height
                    
                    if index == 0 {
                        mutableGroup.isExpanded = true
                    }
                    
                    return mutableGroup
                }
                
                self.tableView.reloadData()
            }
        }
    }
    
    private func createCustomTitleView() -> UIView {
        let titleLabel = UILabel()
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.textColor = Colors.charcoalColor
        titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
        titleLabel.text = "Complete car details".localized
        
        let titleView = UIView()
        titleView.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: titleView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: titleView.centerYAnchor)
        ])
        
        return titleView
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        #if DEVELOPMENT
        #else
        Analytics.logEvent("sell_car_features_screen", parameters: [:])
		#endif
    }
    
    private func addNavigationButton() {
        let barButtonItem = UIBarButtonItem.init(
            title: "Cancel".localized,
            style: .plain,
            target: self,
            action: #selector(closePage)
        )
        
        barButtonItem.tintColor = Colors.barButtonItemTintColor
        
        barButtonItem.setTitleTextAttributes(
            [.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14) ?? UIFont()],
            for: .normal
        )
        
        self.navigationItem.rightBarButtonItem = barButtonItem
    }
    
    @objc
    private func closePage(_ sender:Any) {
        self.showWarningDialog()
    }
    
    private func sendAbandonedRequest(requestFrom: Int) {
        var mileageId: Int = 0
        var yearId: Int = 0
        var trimId: Int = 0
        
        for (key, dataAttribute) in self.selfSellingUserData ?? [:] {
            if let dataAttribute = dataAttribute as? DataAttribute,
               let dataKey = dataAttribute.dataKey {
                switch dataKey {
                    case "Mileage":
                        mileageId = dataAttribute.id ?? 0
                    case "Year":
                        yearId = dataAttribute.id ?? 0
                    case "TrimID":
                        trimId = dataAttribute.id ?? 0
                    default:
                        break
                }
            }
        }
        
        if ((self.selfSellingUserData?["0"] as? DataAttribute )?.id ?? 0) != 0 {
            self.carDetailsViewModel.postAbandonedRequest(
                brandID: (self.selfSellingUserData?["0"] as? DataAttribute)?.id ?? 0,
                modelID: (self.selfSellingUserData?["1"] as? DataAttribute)?.id ?? 0,
                trimID: trimId,
                year: yearId,
                mileage: mileageId,
                estimatedPrice: (self.selfSellingUserData?[self.isTrim ? "5" : "4"] as? DataAttribute)?.id ?? 0,
                requestFrom: requestFrom,
                packageId: self.selectedPakcage?.packageId
            ).bind { result in
                if ConstantsValues.sharedInstance.abandonedRequestID == 0 {
                    ConstantsValues.sharedInstance.abandonedRequestID = result?.abandonedRequestID ?? 0
                }
            }.disposed(by: carDetailsViewModel.getDisposeBag())
        }
    }
    
    private func showWarningDialog() {
        let alert = UIAlertController(title: nil, message:"Are you sure you want to exit?".localized, preferredStyle: .alert)
        
        let yesAction = UIAlertAction(title: "Yes".localized, style: .default) { alert in
            if self.isOpenFromEditDashboard {
                for vc in self.navigationController?.viewControllers ?? [] {
                    if vc.isKind(of: MyCarsVC.self) {
                        self.navigationController?.popToViewController(vc, animated: true)
                    }
                }
            } else if self.openFromEditAfterCheckout {
                for vc in self.navigationController?.viewControllers ?? [] {
                    if vc.isKind(of: EditSelfServiceCarAfterCheckoutVC.self) {
                        self.navigationController?.popToViewController(vc, animated: true)
                    }
                }
            } else {
                #if DEVELOPMENT
                #else
                Analytics.logEvent("sell_car_cancel", parameters: [
                    "trigger" : "car_features",
                    "confirm_cancel" : "yes",
                    "package": (self.selectedPakcage?.packageName ?? "").lowercased()
                ])
				#endif
                
                SelfServiceDataSource.shared.resetResult()
                NotificationCenter.default.post(name: NSNotification.Name("AbandonedRequestSent"), object: nil)
                self.navigationController?.popToRootViewController(animated: true)
            }
        }
        
        let cancelAction = UIAlertAction(title: "Cancel".localized, style: .cancel) { _ in
            if self.isOpenFromEditDashboard == false && self.openFromEditAfterCheckout == false {
				#if DEVELOPMENT
				#else
                Analytics.logEvent("sell_car_cancel", parameters: [
                    "trigger" : "car_features",
                    "confirm_cancel" : "no",
                    "package": (self.selectedPakcage?.packageName ?? "").lowercased()
                ])
				#endif
            }
        }
        
        let messageAttrString = NSMutableAttributedString(
            string: "Are you sure you want to exit?".localized,
            attributes: [
                NSAttributedString.Key.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14.0)!
            ]
        )
        
        alert.setValue(messageAttrString, forKey: "attributedMessage")
        
        alert.addAction(yesAction)
        alert.addAction(cancelAction)
        
        self.present(alert, animated: true)
    }
    
    public func setNeededData(uploadedImages: [UploadImageItemModel]?, selfSellingUserData: [String: Any]?, userSelectedNewData: [Int: Any]?, selectedPakcage: LstPackages?, lstPackages: [LstPackages], carDetailsViewModel: CarDetailsViewModel, isTrim: Bool) {
        self.uploadedImages = uploadedImages
        self.selfSellingUserData = selfSellingUserData
        self.userSelectedNewData = userSelectedNewData
        self.selectedPakcage = selectedPakcage
        self.lstPackages = lstPackages
        self.carDetailsViewModel = carDetailsViewModel
        self.isTrim = isTrim
    }
    
    private func goToCheckoutScreen() {
        let checkoutVC = self.getNextViewController(viewControllerClass: SYCProcessCheckoutVC.self, storyBoardName: "SellYouCar", identifier: "SYCProcessCheckoutVC") ?? SYCProcessCheckoutVC()
        checkoutVC.setUserDataForSelfService(uploadedImages: self.uploadedImages, selfSellingUserData: self.selfSellingUserData, userSelectedNewData: self.userSelectedNewData, selectedPakcage: self.selectedPakcage, isTrim: self.isTrim)
        checkoutVC.setAllPackagesArray(allPakcages: self.lstPackages)
        checkoutVC.setIsWantMotorgyOfferSelfService(isWantMotorgyOfferSelfService: self.isWantMotorgyOfferSelfService)
        self.navigationController?.pushViewController(checkoutVC, animated: true)
    }
    
    private func addAdditionalNotesValueIfExists() {
        if self.additionalNotes != "" {
            self.userSelectedNewData?[SelfSellingScreensType.featuresAdditionalNote.rawValue] = [
                SelectedSelfSellingData(name: "AdditionalInfo", id: 0, additionalInfo: self.additionalNotes)
            ]
        } else {
            self.userSelectedNewData?.removeValue(forKey: SelfSellingScreensType.featuresAdditionalNote.rawValue)
        }
    }
    
    @objc
    private func continueAction() {
        if self.isOpenFromEditDashboard {
            self.selectedFeaturesIds.removeDuplicates()
            self.delegate?.sendNewFeaturesTags(selectedIds: self.selectedFeaturesIds, additionalNote: self.additionalNotes != "" ? self.additionalNotes : "")
            self.navigationController?.popViewController(animated: true)
        } else if self.openFromEditAfterCheckout {
            self.selectedFeaturesIds.removeDuplicates()
            self.checkoutDelegate?.sendNewFeaturesTags(selectedIds: self.selectedFeaturesIds, additionalNote: self.additionalNotes != "" ? self.additionalNotes : "")
            self.navigationController?.popViewController(animated: true)
        } else {
            #if DEVELOPMENT
            #else
            Analytics.logEvent("sell_car_features", parameters: ["type": "continue_car_features"])
			#endif
            
            
			self.sendAbandonedRequest(requestFrom: 8)
            
            self.selectedFeaturesIds.removeDuplicates()
            
            self.userSelectedNewData?[SelfSellingScreensType.features.rawValue] = [
                SelectedSelfSellingData(name: "ExtraFeatureIds", id: 0, ids: self.selectedFeaturesIds)
            ]
            
            addAdditionalNotesValueIfExists()
            
            if ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted {
                self.goToConfirmScreenListingCarMicroDealer()
            } else {
                self.goToCashOfferScreen()
            }
        }
    }
    
    @objc
    private func skipAction() {
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            self.navigationController?.popViewController(animated: true)
        } else {
            //            self.additionalNotes = ""
            //            self.userSelectedNewData?.removeValue(forKey: SelfSellingScreensType.featuresAdditionalNote.rawValue)
            
			#if DEVELOPMENT
			#else
            Analytics.logEvent("sell_car_features_skip", parameters: [:])
			#endif
            
			self.sendAbandonedRequest(requestFrom: 8)
            
            //            self.selectedFeaturesIds.removeAll()
            
            //            self.userSelectedNewData?.removeValue(forKey: SelfSellingScreensType.features.rawValue)
            
            //            self.stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
            
            //            DispatchQueue.main.async {
            //                self.lstExtraOptionss.enumerated().forEach { _, options in
            //                    let optionView = SelfServiceFeaturesTVC()
            //                    optionView.configureCell(lstExtraOptionss: options)
            //                    optionView.delegate = self
            //                    self.stackView.addArrangedSubview(optionView)
            //                }
            //
            //                self.additionalNotesView.delegate = self
            //                self.additionalNotesView.resetTextView()
            //                self.stackView.addArrangedSubview(self.additionalNotesView)
            //            }
            
            if ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted {
                self.goToConfirmScreenListingCarMicroDealer()
            } else {
                self.goToCashOfferScreen()
            }
        }
    }
    
    private func goToConfirmScreenListingCarMicroDealer() {
        let viewModel = CarListingConfirmationViewModel(selfSellingUserData: self.selfSellingUserData ?? [:], isTrim: self.isTrim)
        self.carListingConfirmationViewModel = viewModel

        var confirmationView = CarListingConfirmationView(viewModel: viewModel)
        confirmationView.onCancelScreen = { [weak self] in
            self?.navigationController?.popToRootViewController(animated: true)
        }
        confirmationView.listMyCarAction = { [weak self] in
            self?.listMyCarMicroDealer()
        }
        confirmationView.editCarDetails = { [weak self] in
            self?.editCarDetailsScreenMicroDealer()
        }
        confirmationView.editBundle = { [weak self] activeBundles in
            self?.editBundle(activeBundles: activeBundles)
        }

        let host = HostingController(rootView: confirmationView, hideNavigationBar: true)
        self.navigationController?.pushViewController(host, animated: true)
    }
    
    private func editBundle(activeBundles: [BundleModelMyCars]) {
        let bundleSheet = BundleSelectionDialog(
            isPresented: .constant(true),
            bundles: activeBundles,
            selectedBundleId: ConstantsValues.sharedInstance.activeSubscriptionMicroDealer?.id ?? 0,
            onBundleSelected: { [weak self] selectedBundle in
                self?.dismiss(animated: true) {
                    // Update the active subscription
                    ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = selectedBundle

                    // Refresh the UI to reflect the new bundle selection
                    self?.refreshUIAfterBundleChange()
                }
            },
            onCancel: { [weak self] in
                self?.dismiss(animated: true)
            },
            onDismiss: { [weak self] in
                self?.dismiss(animated: true)
            }
        )
        
        let hostingController = UIHostingController(rootView: bundleSheet)
        hostingController.modalPresentationStyle = .custom
        hostingController.view.backgroundColor = .clear
        self.present(hostingController, animated: true)
    }

    /// Refreshes the UI after a bundle change to reflect the new selected bundle
    private func refreshUIAfterBundleChange() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // Update tags list view with new package ID if visible
            if !self.tagsListView.isHidden {
                self.tagsListView.configureViewForSelfSellingSteps(
                    .additionalInformation,
                    tagListType: .selfServiceSteps,
                    currentSelectedPackageId: self.selectedPakcage?.packageId ?? 0
                )
            }

            // Update any bundle-dependent UI elements in this view controller
            self.updateBundleDependentUI()

            // Refresh the CarListingConfirmationViewModel (SwiftUI view) if it exists
            self.refreshConfirmationViewModel()
        }
    }

    /// Updates UI elements that depend on the active bundle
    private func updateBundleDependentUI() {
        // Update continue button state if needed
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            self.continueButton.isHidden = false
        } else {
            self.continueButton.isHidden = self.selectedFeaturesIds.isEmpty
        }

        // Any other bundle-dependent UI updates can be added here
        // For example, if certain features are only available with specific bundles
    }

    /// Refreshes the CarListingConfirmationViewModel to reflect bundle changes
    private func refreshConfirmationViewModel() {
        // Update the CarListingConfirmationViewModel with the new bundle data
        // This will trigger SwiftUI to refresh the view since the properties are @Published
        self.carListingConfirmationViewModel?.updateBundleData()
    }
    
	private func editCarDetailsScreenMicroDealer() {
		// Remove back button text to show only the back arrow
		self.navigationItem.backButtonTitle = ""
		
		let vc = self.getNextViewController(viewControllerClass: EditSelfServiceCarAfterCheckoutVC.self, storyBoardName: "SelfService", identifier: "EditSelfServiceCarAfterCheckoutVC") ?? EditSelfServiceCarAfterCheckoutVC()
		vc.setUserDataForSelfService(
			uploadedImages: self.uploadedImages,
			selfSellingUserData: self.selfSellingUserData ?? [:],
			userSelectedNewData: self.userSelectedNewData,
			selectedPakcage: nil as LstPackages?,
			isTrim: self.isTrim,
			cachedPackagesData: [],
			adIdToPostApi: 0
		)
		vc.setMicroDealerListCarFlow(true)
		vc.popPackForMicroDealerListFlowContinue = { [weak self] (newUserData: [String: Any], newSelectedData: [Int: Any]?, newIsTrim: Bool) in
			self?.selfSellingUserData = newUserData
			self?.userSelectedNewData = newSelectedData
			self?.isTrim = newIsTrim
			self?.carListingConfirmationViewModel?.updateCarData(newUserData: newUserData, newIsTrim: newIsTrim)
		}
		self.navigationController?.pushViewController(vc, animated: true)
	}
    
    private func listMyCarMicroDealer() {
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "12" : "11")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "13" : "12")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "6" : "5")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "7" : "6")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "9" : "8")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "8" : "7")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "10" : "9")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "11" : "10")
        self.selfSellingUserData?.removeValue(forKey: self.isTrim ? "15" : "14")
        
        var reportFile: File = File(data: Data(), name: "")
        var nameToSingleIDDict: [String: Int] = [:]
        var nameToMultipleIDsDict: [String: [Int]] = [:]
        var carDetailsData: [String: Int] = [:]
        var bodyDamageParts: [CustomBodyPart] = []
        var featuresAdditionalInfo: String = ""
        
        for (_, valueArray) in self.userSelectedNewData ?? [:] {
            if let dataArray = valueArray as? [SelectedSelfSellingData] {
                for data in dataArray {
                    if let id = data.id, id != 0 {
                        nameToSingleIDDict[data.name] = id
                    }
                    
                    if let ids = data.ids, !ids.isEmpty, data.id == 0 {
                        nameToMultipleIDsDict[data.name] = ids
                    }
                    
                    if let customBodyParts = data.customBodyParts {
                        bodyDamageParts = customBodyParts
                    }
                }
            }
        }
        
        for (_, valueArray) in self.userSelectedNewData ?? [:] {
            if let dataArray = valueArray as? [SelectedSelfSellingData] {
                for data in dataArray {
                    if let uploadedFile = data.reportFile {
                        reportFile = uploadedFile
                        break
                    }
                }
            }
        }
        
        for (_, valueArray) in self.userSelectedNewData ?? [:] {
            if let dataArray = valueArray as? [SelectedSelfSellingData] {
                for data in dataArray {
                    if let featuresAdditionalInfoString = data.additionalInfo {
                        featuresAdditionalInfo = featuresAdditionalInfoString
                        break
                    }
                }
            }
        }
        
        if let userData = self.selfSellingUserData as? [String: DataAttribute] {
            let nonZerosIdValuesDictionary = userData.filter { $1.id != 0 }
            
            for (_, value) in nonZerosIdValuesDictionary {
                carDetailsData[value.dataKey] = value.id
            }
        }
    
        self.myCarDetailsViewModel.listCarMicroDealer(stringToIntData: nameToSingleIDDict, stringToIdsData: nameToMultipleIDsDict, images: self.uploadedImages, file: reportFile, carDetailsData: carDetailsData, bodyDamageParts: bodyDamageParts, additionalInfo: featuresAdditionalInfo, isWantMotorgyOffer: self.isWantMotorgyOfferSelfService, subscriptionId: ConstantsValues.sharedInstance.activeSubscriptionMicroDealer?.id ?? 0).bind { [weak self] apiResult in
            DispatchQueue.main.async {
                if let apiResult = apiResult {
                    if apiResult.aPIStatus ?? 0 == 1 {
                        self?.goToDashboard(adId: apiResult.adIID ?? 0, isSelfServiceCar: true)
                    } else {
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: apiResult.aPIMessage ?? "Something went wrong".localized, buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                    }
                } else {
                    AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                }
            }
        }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
    }
    
    private func goToDashboard(adId: Int, isSelfServiceCar: Bool) {
        ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
        ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let tabBar = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
            self.navigationController?.setViewControllers([tabBar], animated: false)
            tabBar.selectedIndex = 3
            
            if let myGarageVC = tabBar.viewControllers?[3] as? MyGarageVC {
                if let navController = myGarageVC.navigationController {
                    
                    let myCarsVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarsVC") as! MyCarsVC
                    myCarsVC.setupFlagsFromMyGarageScreen(isBuying: false, isSelling: true)
                    
                    let myCarStatusVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarStatusVC") as! MyCarStatusVC
                    myCarStatusVC.setCarId(carId: adId, isSelfServiceCar: isSelfServiceCar)
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        navController.pushViewController(myCarsVC, animated: false)
                        navController.pushViewController(myCarStatusVC, animated: true)
                    }
                }
            }
        }
    }
    
    private func goToCashOfferScreen() {
        let cashVC = CashOfferHostingController()
        cashVC.onGetCashOffer = { [weak self] in
            DispatchQueue.main.async {
                self?.isWantMotorgyOfferSelfService = true
                self?.goToCheckoutScreen()
            }
        }
        cashVC.onSkip = { [weak self] in
            DispatchQueue.main.async {
                self?.isWantMotorgyOfferSelfService = false
                self?.goToCheckoutScreen()
            }
        }
        navigationController?.pushViewController(cashVC, animated: false)
    }
    
    private func calculateHeightForSubArrayInsideEachGroup(_ options: [ExtraOptions]) -> CGFloat {
        // Use the same font as TagCollectionViewCell for consistency
        let font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 12 : 10) ?? UIFont()

        // Collection view layout spacing (matches LeftAlignedCollectionViewFlowLayout)
        let itemSpacing: CGFloat = 12.0
        let lineSpacing: CGFloat = 12.0

        // Calculate available width for collection view
        // Table view cell margins (24 left + 24 right) + collection view margins (16 left + 16 right)
        let tableViewHorizontalPadding: CGFloat = 48.0
        let collectionViewHorizontalPadding: CGFloat = 32.0
        let collectionViewWidth: CGFloat = UIScreen.main.bounds.width - tableViewHorizontalPadding - collectionViewHorizontalPadding

        var currentRowWidth: CGFloat = 0
        var numberOfRows = 0

        options.forEach { option in
            // Use boundingRect for more accurate text size calculation
            let textSize = (option.name as NSString?)?.boundingRect(
                with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
                options: [.usesLineFragmentOrigin, .usesFontLeading],
                attributes: [.font: font],
                context: nil
            ).size ?? .zero

            // Match TagCollectionViewCell size calculation: text width + horizontal padding (16 + 16) + extra margin for text rendering
            // Arabic text may need more space, so add more padding for Arabic
            let extraHorizontalPadding: CGFloat = LanguageHelper.isEnglish ? 8 : 12
            let totalHorizontalPadding: CGFloat = 32 + extraHorizontalPadding
            let itemWidth = ceil(textSize.width + totalHorizontalPadding)

            // Check if item fits in current row (accounting for spacing between items)
            let widthNeeded = currentRowWidth == 0 ? itemWidth : currentRowWidth + itemSpacing + itemWidth

            if widthNeeded <= collectionViewWidth {
                currentRowWidth = widthNeeded
            } else {
                // Start new row
                numberOfRows += 1
                currentRowWidth = itemWidth
            }
        }

        // Add the last row if there are items
        if currentRowWidth > 0 {
            numberOfRows += 1
        }

        // Calculate item height: text height + vertical padding (12 + 12)
        let textHeight = ("Sample" as NSString).boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        ).size.height
        let itemHeight = max(ceil(textHeight + 24), 44)

        // Calculate total height: (number of rows * item height) + (spacing between rows)
        let totalHeight = (CGFloat(numberOfRows) * itemHeight) + (CGFloat(max(0, numberOfRows - 1)) * lineSpacing)
        return totalHeight
    }
}

extension SelfServiceFeaturesVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return SelfServiceFeaturesVCSections.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == SelfServiceFeaturesVCSections.features.rawValue {
            return self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ? self.editableLstExtraOptionss.count : self.lstExtraOptionss.count
        } else {
            return 1
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
            case SelfServiceFeaturesVCSections.features.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "SelfServiceEnhancedFeaturesTVC", for: indexPath) as! SelfServiceEnhancedFeaturesTVC
                cell.selectionStyle = .none
                cell.configureCell(lstExtraOptionss: self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ? self.editableLstExtraOptionss[indexPath.row] : self.lstExtraOptionss[indexPath.row], indexPath: indexPath)
                cell.delegate = self
                return cell
                
            case SelfServiceFeaturesVCSections.divider.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: FeaturesDividerCell.identifier, for: indexPath) as! FeaturesDividerCell
                cell.selectionStyle = .none
                return cell
                
            case SelfServiceFeaturesVCSections.featuresAdditionalNote.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: BodyPartAdditionalNotesTVC.identifier, for: indexPath) as! BodyPartAdditionalNotesTVC
                cell.selectionStyle = .none
                cell.configureCell(note: self.additionalNotes, cellType: .features)
                cell.delegate = self
                return cell
                
            default:
                return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.section == SelfServiceFeaturesVCSections.features.rawValue {
            let item = self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ? self.editableLstExtraOptionss[indexPath.row] : self.lstExtraOptionss[indexPath.row]
            
            let headerHeight: CGFloat = 56
            let padding: CGFloat = 32
            let extraPadding: CGFloat = LanguageHelper.isEnglish ? 12 : 30
            
            if item.isExpanded {
                return headerHeight + item.subArrayHeight + padding + extraPadding
            } else {
                return headerHeight + 16
            }
        } else {
            return UITableView.automaticDimension
        }
    }
}

extension SelfServiceFeaturesVC: SelfServiceFeaturesFullScreenVCDelegate {
    func sendSelectedTagsIds(parentObject: LstExtraOptionss?) {
        if let obj = parentObject {
            var isUpdated = false
            
            if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
                if let index = self.editableLstExtraOptionss.firstIndex(where: { $0.groupId == obj.groupId }) {
                    self.editableLstExtraOptionss[index] = obj
                    isUpdated = true
                }
            } else {
                if let index = self.lstExtraOptionss.firstIndex(where: { $0.groupId == obj.groupId }) {
                    self.lstExtraOptionss[index] = obj
                    isUpdated = true
                }
            }
            
            if isUpdated {
                let selectedIdsFromObj = obj.extraOptions?.filter({ $0.isSelected }).compactMap({ $0.id }) ?? []

                self.selectedFeaturesIds.removeAll { id in
                    obj.extraOptions?.contains(where: { $0.id == id && !$0.isSelected }) ?? false
                }
                self.selectedFeaturesIds.append(contentsOf: selectedIdsFromObj)
                self.selectedFeaturesIds.removeDuplicates()

                // Recalculate height for the updated group
                let displayOptions = obj.displayOptions ?? []
                let newHeight = self.calculateHeightForSubArrayInsideEachGroup(displayOptions)

                if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
                    if let index = self.editableLstExtraOptionss.firstIndex(where: { $0.groupId == obj.groupId }) {
                        self.editableLstExtraOptionss[index].subArrayHeight = newHeight
                    }
                } else {
                    if let index = self.lstExtraOptionss.firstIndex(where: { $0.groupId == obj.groupId }) {
                        self.lstExtraOptionss[index].subArrayHeight = newHeight
                    }
                }

                self.setupDataSource()
            }
        }
    }
}

extension SelfServiceFeaturesVC: SelfServiceEnhancedFeaturesTVCProtocol {
    func didSelect(item: Int, indexPath: IndexPath, lstExtraOptionss: LstExtraOptionss?) {
        if item == -1 {
            let selfServiceFeaturesFullScreenVC = self.getNextViewController(viewControllerClass: SelfServiceFeaturesFullScreenVC.self, storyBoardName: "SelfService", identifier: "SelfServiceFeaturesFullScreenVC") ?? SelfServiceFeaturesFullScreenVC()
            selfServiceFeaturesFullScreenVC.setData(lstExtraOptionss: lstExtraOptionss, controller: self)
            self.navigationController?.pushViewController(selfServiceFeaturesFullScreenVC, animated: true)
            return
        }
        
        var options: [ExtraOptions]?
        
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            options = self.editableLstExtraOptionss[indexPath.row].extraOptions
        } else {
            options = self.lstExtraOptionss[indexPath.row].extraOptions
        }
        
        guard var mutableOptions = options, let index = mutableOptions.firstIndex(where: { $0.id == item }) else { return }
        
        mutableOptions[index].isSelected.toggle()
        
        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            self.editableLstExtraOptionss[indexPath.row].extraOptions = mutableOptions
            
            var displayOptions = Array(mutableOptions.prefix(7))
            
            if mutableOptions.count > 7 {
                let moreOption = ExtraOptions(id: -1, name: "More".localized)
                displayOptions.append(moreOption)
            }
            
            self.editableLstExtraOptionss[indexPath.row].displayOptions = displayOptions
        } else {
            self.lstExtraOptionss[indexPath.row].extraOptions = mutableOptions
            
            var displayOptions = Array(mutableOptions.prefix(7))
            
            if mutableOptions.count > 7 {
                let moreOption = ExtraOptions(id: -1, name: "More".localized)
                displayOptions.append(moreOption)
            }
            
            self.lstExtraOptionss[indexPath.row].displayOptions = displayOptions
        }
        
        if self.selectedFeaturesIds.contains(item) {
            if let itemToRemoveIndex = self.selectedFeaturesIds.firstIndex(of: item) {
                self.selectedFeaturesIds.remove(at: itemToRemoveIndex)
            }
        } else {
            self.selectedFeaturesIds.append(item)
        }

        // Recalculate height for the updated group
        let displayOptions = self.isOpenFromEditDashboard || self.openFromEditAfterCheckout ?
            self.editableLstExtraOptionss[indexPath.row].displayOptions :
            self.lstExtraOptionss[indexPath.row].displayOptions

        let newHeight = self.calculateHeightForSubArrayInsideEachGroup(displayOptions ?? [])

        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            self.editableLstExtraOptionss[indexPath.row].subArrayHeight = newHeight
        } else {
            self.lstExtraOptionss[indexPath.row].subArrayHeight = newHeight
        }

        DispatchQueue.main.async {
            if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
                self.continueButton.isHidden = false
            } else {
                self.continueButton.isHidden = self.selectedFeaturesIds.isEmpty
            }

            self.tableView.reloadRows(at: [indexPath], with: .automatic)
        }
    }
    
    func expandCollapse(indexPath: IndexPath?) {
        guard let index = indexPath else { return }

        // Collect indices of rows that will change
        var rowsToUpdate: [IndexPath] = []

        if self.isOpenFromEditDashboard || self.openFromEditAfterCheckout {
            guard self.editableLstExtraOptionss[index.row].isExpanded == false else { return }

            // Find currently expanded rows to collapse them
            for (idx, group) in self.editableLstExtraOptionss.enumerated() {
                if group.isExpanded && idx != index.row {
                    rowsToUpdate.append(IndexPath(row: idx, section: index.section))
                }
            }

            // Add the row that will be expanded
            rowsToUpdate.append(index)

            self.editableLstExtraOptionss = self.editableLstExtraOptionss.map { group in
                var mutableGroup = group
                mutableGroup.isExpanded = false
                return mutableGroup
            }

            self.editableLstExtraOptionss[index.row].isExpanded.toggle()
        } else {
            guard self.lstExtraOptionss[index.row].isExpanded == false else { return }

            // Find currently expanded rows to collapse them
            for (idx, group) in self.lstExtraOptionss.enumerated() {
                if group.isExpanded && idx != index.row {
                    rowsToUpdate.append(IndexPath(row: idx, section: index.section))
                }
            }

            // Add the row that will be expanded
            rowsToUpdate.append(index)

            self.lstExtraOptionss = self.lstExtraOptionss.map { group in
                var mutableGroup = group
                mutableGroup.isExpanded = false
                return mutableGroup
            }

            self.lstExtraOptionss[index.row].isExpanded.toggle()
        }

        // Animate the changes smoothly
        DispatchQueue.main.async {
            // Use a custom animation for smoother expand/collapse
            UIView.animate(withDuration: 0.4, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: [.curveEaseInOut, .allowUserInteraction], animations: {
                self.tableView.performBatchUpdates({
                    self.tableView.reloadRows(at: rowsToUpdate, with: .fade)
                }, completion: nil)
            }, completion: nil)
        }
    }
}

extension SelfServiceFeaturesVC: InspectionHomeAdditionalDetailsTVCProtocol {
    func keyboardDidShow(notification: NSNotification) {
        let userInfo = notification.userInfo
        let keyboardSize = userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue
        let keyboardHeight = keyboardSize.cgRectValue.height
        
        if view.frame.origin.y == CGFloat(0) {
            view.frame.origin.y -= keyboardHeight
        }
    }
    
    func keyboardDidHide(notification: NSNotification) {
        view.frame.origin.y = CGFloat(0)
    }
    
    func userDidTypeAdditionalDetails(text: String) {
        self.additionalNotes = text
    }
}

extension SelfServiceFeaturesVC {
    public func setDataForEditFromDashboard(selectedFeaturesIds: [Int]?, isOpenFromEditDashboard: Bool, controller: EditSelfServiceCarDetailsVC?, additionalNote: String?, checkoutController: EditSelfServiceCarAfterCheckoutVC? = nil) {
        self.delegate = controller
        self.checkoutDelegate = checkoutController
        self.selectedFeaturesIds = selectedFeaturesIds ?? []
        self.additionalNotes = additionalNote ?? ""
        self.isOpenFromEditDashboard = isOpenFromEditDashboard
    }
    
    public func setDataForEditFromAfterCheckout(selectedFeaturesIds: [Int]?, controller: EditSelfServiceCarDetailsVC?, additionalNote: String?, checkoutController: EditSelfServiceCarAfterCheckoutVC? = nil, openFromEditAfterCheckout: Bool) {
        self.delegate = controller
        self.checkoutDelegate = checkoutController
        self.selectedFeaturesIds = selectedFeaturesIds ?? []
        self.additionalNotes = additionalNote ?? ""
        self.openFromEditAfterCheckout = openFromEditAfterCheckout
    }
}
