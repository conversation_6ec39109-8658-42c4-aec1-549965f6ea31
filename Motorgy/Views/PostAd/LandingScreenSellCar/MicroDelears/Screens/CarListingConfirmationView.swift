//
//  CarListingConfirmationView.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 20/07/2025.
//  Copyright 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI
import Combine

class CarListingConfirmationViewModel: ObservableObject {
    @Published var selfSellingUserData: [String: Any]
    @Published var isTrim: Bool
    @Published var firstText: String = ""
    @Published var secondText: String = ""
    @Published var showEditBundleButton: Bool = false
    @Published var bundlePackageName: String = ""
    @Published var bundleBoostPackageName: String = ""

    init(selfSellingUserData: [String: Any], isTrim: Bool) {
        self.selfSellingUserData = selfSellingUserData
        self.isTrim = isTrim
        self.setupUserData()
        self.updateBundleData()
    }

    func updateCarData(newUserData: [String: Any], newIsTrim: Bool) {
        DispatchQueue.main.async {
            self.selfSellingUserData = newUserData
            self.isTrim = newIsTrim
            self.setupUserData()
        }
    }

    func updateBundleData() {
        DispatchQueue.main.async {
            self.bundlePackageName = ConstantsValues.sharedInstance.activeSubscriptionMicroDealer?.packageName ?? ""
            self.bundleBoostPackageName = ConstantsValues.sharedInstance.activeSubscriptionMicroDealer?.boostPackageName ?? ""
        }
    }

    private func setupUserData() {
        let brandName = (self.selfSellingUserData["0"] as? DataAttribute)?.name ?? ""
        let modelName = (self.selfSellingUserData["1"] as? DataAttribute)?.name ?? ""
        let trimName = (self.selfSellingUserData["2"] as? DataAttribute)?.name ?? ""
        let yearName = (self.selfSellingUserData["\(self.isTrim ? 3 : 2)"] as? DataAttribute)?.name ?? ""

        let mileage = (self.selfSellingUserData["\(self.isTrim ? 4 : 3)"] as? DataAttribute)?.name ?? ""
        var mileageName = mileage == "Mileage" ? "0 " + "KM".localized : mileage

        let newFirstText: String
        if self.isTrim {
            newFirstText = brandName + " " + modelName + " " + trimName + " " + yearName
        } else {
            newFirstText = brandName + " " + modelName + " " + yearName
        }

        let price = (self.selfSellingUserData["\(self.isTrim ? 5 : 4)"] as? DataAttribute)?.id ?? 0
        let priceName = "\(price.withCommas())"

        if mileageName == "0.0" {
            mileageName = "0"
        }

        if !mileageName.contains("KM".localized) {
            mileageName = mileageName + " " + "KM".localized
        }

        let newSecondText = price == 0 ? "\(mileageName)" : "\(mileageName) - \(priceName) " + "KWD".localized

        self.firstText = newFirstText
        self.secondText = newSecondText
    }
}

struct CarListingConfirmationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showingCancelAlert = false
    @StateObject private var viewModel: CarListingConfirmationViewModel
    private var microDealerViewModel: MicroDealerViewModel?
    var onCancelScreen: () -> Void = {}
    var listMyCarAction: () -> Void = {}
    var editCarDetails: () -> Void = {}
    var editBundle: ([BundleModelMyCars]) -> Void = { _ in }


    init(viewModel: CarListingConfirmationViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
        self.microDealerViewModel = MicroDealerViewModel(navigationController: nil)
    }

    init(selfSellingUserData: [String : Any], isTrim: Bool) {
        self._viewModel = StateObject(wrappedValue: CarListingConfirmationViewModel(selfSellingUserData: selfSellingUserData, isTrim: isTrim))
        self.microDealerViewModel = MicroDealerViewModel(navigationController: nil)
    }
    

    
    private func getActiveSubscriptions() {
        self.microDealerViewModel?.getActiveSubscriptionsForMicroDealer { [weak viewModel] result in
            DispatchQueue.main.async {
                viewModel?.showEditBundleButton = result?.activeSubscriptions?.count ?? 0 > 0
            }
        }
    }

    func updateCarData(newUserData: [String: Any], newIsTrim: Bool) {
        viewModel.updateCarData(newUserData: newUserData, newIsTrim: newIsTrim)
    }
    
    @ViewBuilder
    var customNavigationBar: some View {
        HStack(alignment: .center) {
            Button {
                dismiss()
            } label: {
                Image(systemName: LanguageHelper.isEnglish ? "chevron.left" : "chevron.right")
                    .foregroundColor(.primary)
                    .font(.system(size: 18, weight: .medium))
            }
            
            Spacer()
            
            Text(LanguageHelper.isEnglish ? "Confirmation" : "تأكيد")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                        .weight(.semibold)
                )
                .foregroundColor(.black)
            
            Spacer()
            
            Button("Cancel".localized) {
                showingCancelAlert = true
            }
            .foregroundColor(Color.init(uiColor: Colors.bluishColor))
            .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
    
    @ViewBuilder
    var carDetailsCard: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(viewModel.firstText)
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14))
                        .foregroundColor(.black)

                    Text(viewModel.secondText)
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14))
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Button {
                    editCarDetails()
                } label: {
                    Image("edit_sell_car")
                        .resizable()
                        .frame(width: 30, height: 30)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            
            Spacer(minLength: 0)
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    @ViewBuilder
    var selectedBundleCard: some View {
        VStack(alignment: .leading, spacing: 0) {
			HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(LanguageHelper.isEnglish ? "SELECTED BUNDLE" : "الباقة المختارة")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16))
						.foregroundColor(Color.init(uiColor: Colors.slateColor))
                    
                    HStack(spacing: 4) {
                        Text(viewModel.bundlePackageName)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(.black)

                        if !viewModel.bundleBoostPackageName.isEmpty && viewModel.bundleBoostPackageName != "<null>" {
                            Text("+")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .foregroundColor(.black)

                            Text(viewModel.bundleBoostPackageName)
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .foregroundColor(.black)
                                .background(alignment: .bottom) {
                                    Color.yellow
                                        .opacity(0.4)
                                        .frame(height: 4)
                                }
                        }
                    }
                }
                
                Spacer()
                
                if viewModel.showEditBundleButton {
                    Button {
                        editBundle(self.microDealerViewModel?.activeSubscriptions ?? [])
                    } label: {
                        Image("edit_sell_car")
                            .resizable()
                            .frame(width: 30, height: 30)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    @ViewBuilder
    var listMyCarButton: some View {
        Button {
            listMyCarAction()
        } label: {
            HStack {
                Text(LanguageHelper.isEnglish ? "List my car" : "أضف سيارتي")
                    .font(
						Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                            .weight(.bold)
                    )
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 40)
            .background(
                Color.init(uiColor: Colors.shamrockColor)
            )
            .cornerRadius(12)
        }
    }
    
    var body: some View {
        ZStack {
            Color(uiColor: UIColor.hexStringToUIColor(hex: "#F2F4F7"))
                .ignoresSafeArea()

            VStack(spacing: 0) {
                customNavigationBar

                ScrollView {
                    VStack(spacing: 16) {
                        carDetailsCard
                        selectedBundleCard
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    .padding(.bottom, 16)
                }

                Spacer()

                VStack(spacing: 0) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 0.5)

                    listMyCarButton
                        .padding(.horizontal, 16)
                        .padding(.vertical, 16)
                }
                .background(Color.white)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: -4)
            }
            
            // Custom Alert Overlay
            if showingCancelAlert {
                ZStack {
                    // Background dimming
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()
                        .onTapGesture {
                            // Prevent dismissal by tapping outside
                        }
                    
                    // Alert Container
                    VStack(spacing: 0) {
                        // Message
                        Text("Are you sure you want to exit?".localized)
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 16))
                            .foregroundColor(Color(UIColor.label))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 16)
                            .padding(.top, 20)
                            .padding(.bottom, 16)
                        
                        Divider()
                            .background(Color(UIColor.separator))
                        
                        // Buttons
                        HStack(spacing: 0) {
                            if LanguageHelper.isEnglish {
                                // Cancel button (left)
                                Button {
                                    showingCancelAlert = false
                                } label: {
                                    Text("Cancel".localized)
                                        .font(.custom("Inter-Regular", size: 15))
                                        .foregroundColor(Color(UIColor.blue))
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 44)
                                }
                                
                                Divider()
                                    .background(Color(UIColor.separator))
                                    .frame(width: 0.5, height: 44)
                                
                                // OK button (right)
                                Button {
                                    showingCancelAlert = false
                                    onCancelScreen()
                                } label: {
                                    Text("Ok".localized)
                                        .font(.custom("Inter-Regular", size: 15))
                                        .foregroundColor(.blue)
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 44)
                                }
                            } else {
                                // OK button (right in RTL, so appears first)
                                Button {
                                    showingCancelAlert = false
                                    onCancelScreen()
                                } label: {
                                    Text("Ok".localized)
                                        .font(.custom("Cairo-Regular", size: 15))
                                        .foregroundColor(.blue)
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 44)
                                }
                                
                                Divider()
                                    .background(Color(UIColor.separator))
                                    .frame(width: 0.5, height: 44)
                                
                                // Cancel button (left in RTL, so appears second)
                                Button {
                                    showingCancelAlert = false
                                } label: {
                                    Text("Cancel".localized)
                                        .font(.custom("Cairo-Regular", size: 15))
                                        .foregroundColor(Color(UIColor.blue))
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 44)
                                }
                            }
                        }
                        .frame(height: 44)
                    }
                    .background(Color(UIColor.systemBackground))
                    .cornerRadius(13)
                    .frame(width: 270)
                    .shadow(radius: 30)
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.2), value: showingCancelAlert)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
        .onAppear {
            getActiveSubscriptions()
        }
    }
}
