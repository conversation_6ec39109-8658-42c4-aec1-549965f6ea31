//
//  LandingSellCarTVHandler.swift
//  Motorgy
//
//  Created by <PERSON><PERSON> on 01/06/2021.
//  Copyright © 2021 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import SwiftUI
import FirebaseAnalytics

protocol LandingSellCarTVHandlerDelegate: AnyObject {
    func editAbandonedAction()
    func continueSellingAction()
    func moveToModelList(brandId: Int)
}

class LandingSellCarTVHandler: NSObject, UITableViewDelegate, UITableViewDataSource, OnButtonClicked {
    
    // MARK: - Values
    private weak var tv: UITableView!
    private weak var controller: BaseVC?
    private var landingSellList: LandingSellCarModel = LandingSellCarModel()
    private var viewModel: ViewModel!
    private var moreLessFlag: Bool?
    private var faqList = [LstFAQObject]()
    private var landingVC: LandingSellCarVC?
    private var headerCanMove = true
    private var postAdVM: PostAdViewModel?
    private var lstPackages: [LstPackages] = []
    private var lstCityWithAreas: [LstCityWithAreas] = []
    private var lstInspectionLocations: [LstInspectionLocations] = []
    private var lstMasterPackages: [LstMasterPackages] = []
    private var isSelfServiceToggleTapped = false
    weak var delegate: LandingSellCarTVHandlerDelegate?
    
    // MARK: - init
    init(tv: UITableView, controller: BaseVC?, viewModel: ViewModel, landingVC: LandingSellCarVC?, postAdVM: PostAdViewModel?) {
        super.init()
        self.tv = tv
        self.controller = controller
        self.viewModel = viewModel
        self.landingVC = landingVC
        self.postAdVM = postAdVM
        self.tv.delegate = self
        self.tv.dataSource = self
        self.tv.register(StaticBadgesHeaderTVC.nib(), forCellReuseIdentifier: StaticBadgesHeaderTVC.identifier)
        self.tv.register(StaticBadgesHeaderTwoTVC.nib(), forCellReuseIdentifier: StaticBadgesHeaderTwoTVC.identifier)
        self.tv.register(AbandonedSectionTVC.nib(), forCellReuseIdentifier: AbandonedSectionTVC.identifier)
        self.tv.register(AnimatingBannersTVC.nib(), forCellReuseIdentifier: AnimatingBannersTVC.identifier)
        self.tv.register(LandingNeedHelpSectionTVC.nib(), forCellReuseIdentifier: LandingNeedHelpSectionTVC.identifier)
        self.tv.register(HowSellingWorksTVC.nib(), forCellReuseIdentifier: HowSellingWorksTVC.identifier)
        self.tv.register(BundleCarsTableViewCell.nib(), forCellReuseIdentifier: BundleCarsTableViewCell.identifier)
        self.tv.register(LandingScreenEnhancedDealerTVC.nib(), forCellReuseIdentifier: LandingScreenEnhancedDealerTVC.identifier)
        self.tv.register(LandingFAQTVC.nib(), forCellReuseIdentifier: LandingFAQTVC.identifier)
        self.tv.register(TogglePackagesTVC.nib(), forCellReuseIdentifier: TogglePackagesTVC.identifier)
    }
    
    // MARK: - showData
    public func showData(landingSellList: LandingSellCarModel) {
        self.landingSellList = landingSellList
        self.faqList = self.landingSellList.lstFAQObject ?? []
        ConstantsValues.sharedInstance.brandsList = self.landingSellList.lstBrands ?? []
        self.lstInspectionLocations = self.landingSellList.lstInspectionLocations ?? []
        self.lstCityWithAreas = self.landingSellList.lstCityWithAreas ?? []
        self.lstMasterPackages = self.landingSellList.lstMasterPackages ?? []
        self.tv.reloadData()
    }
    
    public func moveToDealerWebSite() {
        let webVc = self.controller?.getNextViewController(viewControllerClass: WebVC.self,storyBoardName: "Utilities", identifier: "WebVC") ?? WebVC()
        webVc.setPageUrl(url: "https://www.motorgy.com/\(LanguageHelper.language.currentLanguage())/dealer-register#registrationForm")
        webVc.modalPresentationStyle = .fullScreen
        self.controller?.present(webVc, animated: true, completion: nil)
    }
    
    // MARK: - TableView Delegate and DateSource
    func numberOfSections(in tableView: UITableView) -> Int {
        return LandingScreenTVSections.allCases.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
            case LandingScreenTVSections.staticBadgesHeaderSec.rawValue,
                LandingScreenTVSections.needHelpSec.rawValue,
                LandingScreenTVSections.howSellingWorksSec.rawValue:
                return self.landingSellList.aPIStatus == 1 ? 1 : 0
                
            case LandingScreenTVSections.togglePackagesSec.rawValue:
                return self.landingSellList.aPIStatus == 1 ? 1 : 0
                
            case LandingScreenTVSections.staticBadgesHeaderTwoSec.rawValue:
                return self.landingSellList.abandonedRequest == nil || self.landingSellList.abandonedRequest?.AbandonedRequestId == 0
                	? self.landingSellList.aPIStatus == 1 ? 1 : 0
                	: 0
                
            case LandingScreenTVSections.sellCarSec.rawValue:
                return self.landingSellList.abandonedRequest == nil || self.landingSellList.abandonedRequest?.AbandonedRequestId == 0
                	? self.landingSellList.aPIStatus == 1 ? 1 : 0
                	: 0
                
            case LandingScreenTVSections.abandonedSec.rawValue:
                return self.landingSellList.abandonedRequest == nil || self.landingSellList.abandonedRequest?.AbandonedRequestId == 0 || UserHelper.user.isLogin() == false
                	? 0
                	: self.landingSellList.aPIStatus == 1 ? 1 : 0
                
            case LandingScreenTVSections.bundleSec.rawValue:
                return self.landingSellList.aPIStatus == 1 ? self.landingSellList.haveAnyActiveSubscription ?? false ? 0 : 1 : 0
            
            case LandingScreenTVSections.dealersSec.rawValue:
//                return self.landingSellList.dealerSellYourCarObject != nil ? 1 : 0
                return 0
                
            case LandingScreenTVSections.animatedBannersSec.rawValue:
                return self.landingSellList.aPIStatus == 1 ? 4 : 0
                
            case LandingScreenTVSections.customerReviewSec.rawValue:
                return self.landingSellList.lstCustomerReview?.filter { $0.isFeatured == true }.count ?? 0 >= 1 ? 1 : 0
                
            case LandingScreenTVSections.headerFaqsSec.rawValue:
                return self.landingSellList.lstFAQObject?.count ?? 0 >= 1 ? 1 : 0
                
            case LandingScreenTVSections.faqSec.rawValue:
                return self.landingSellList.lstFAQObject?.count ?? 0
                
            default:
                return 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
                
            case LandingScreenTVSections.staticBadgesHeaderSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: StaticBadgesHeaderTVC.identifier) as! StaticBadgesHeaderTVC
                cell.selectionStyle = .none
                return cell
                
            case LandingScreenTVSections.staticBadgesHeaderTwoSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: StaticBadgesHeaderTwoTVC.identifier) as! StaticBadgesHeaderTwoTVC
                cell.selectionStyle = .none
                cell.configureCell(vc: self.controller, brandsList: ConstantsValues.sharedInstance.brandsList)
                cell.setData(lstPackages: self.lstPackages, lstInspectionLocations: self.lstInspectionLocations, lstCityWithAreas: self.lstCityWithAreas, lstMasterPackages: self.lstMasterPackages)
                cell.delegate = self
                return cell
                
            case LandingScreenTVSections.sellCarSec.rawValue :
                let cell = tableView.dequeueReusableCell(withIdentifier: "SellCarInfoCTV") as! SellCarInfoCTV
                cell.selectionStyle = .none
                cell.confingerCell(vc: self.controller, str: self.landingSellList.priceSellYourCarObject?.title ?? "", postAdVM: postAdVM)
                cell.setData(lstPackages: self.lstPackages, lstInspectionLocations: self.lstInspectionLocations, lstCityWithAreas: self.lstCityWithAreas, lstMasterPackages: self.lstMasterPackages)
                cell.delegate = self
                return cell
                
            case LandingScreenTVSections.abandonedSec.rawValue :
                let cell = tableView.dequeueReusableCell(withIdentifier: AbandonedSectionTVC.identifier, for: indexPath) as! AbandonedSectionTVC
                cell.selectionStyle = .none
                cell.configureCell(abandonedObject: self.landingSellList.abandonedRequest) { [weak self] in
                    DispatchQueue.main.async {
                        self?.delegate?.editAbandonedAction()
                    }
                } continueSellingActionCallback: { [weak self] in
                    DispatchQueue.main.async {
                        self?.delegate?.continueSellingAction()
                    }
                }
                return cell
                
            case LandingScreenTVSections.bundleSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: BundleCarsTableViewCell.identifier) as! BundleCarsTableViewCell
                cell.confingerCell(vc: self.controller)
                return cell
            
            case LandingScreenTVSections.dealersSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: LandingScreenEnhancedDealerTVC.identifier) as! LandingScreenEnhancedDealerTVC
                cell.confingerCell(vc: self.controller, dealerInfo: self.landingSellList.dealerSellYourCarObject)
                return cell
                
            case LandingScreenTVSections.togglePackagesSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: TogglePackagesTVC.identifier) as! TogglePackagesTVC
                cell.selectionStyle = .none
                cell.configureCell(isSelfServiceTapped: self.isSelfServiceToggleTapped)
                cell.didTapSelfServiceView = { [weak self] isSelfService in
                    self?.isSelfServiceToggleTapped = isSelfService
                    DispatchQueue.main.async {
                        self?.tv.reloadData()
                    }
                }
                return cell
                
            case LandingScreenTVSections.howSellingWorksSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: HowSellingWorksTVC.identifier) as! HowSellingWorksTVC
                cell.selectionStyle = .none
                return cell
                
            case LandingScreenTVSections.animatedBannersSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: AnimatingBannersTVC.identifier) as! AnimatingBannersTVC
                cell.selectionStyle = .none
                cell.configureCellwith(index: indexPath, isSelfServiceToggleTapped: self.isSelfServiceToggleTapped)
                return cell
                
            case LandingScreenTVSections.customerReviewSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "CustomerReviewCTV") as! CustomerReviewCTV
                cell.selectionStyle = .none
                cell.confingerCell(
                    vc: self.controller,
                    lstReview: self.landingSellList.lstCustomerReview?.filter { $0.isFeatured == true },
                    lstReviewAll: self.landingSellList.lstCustomerReview
                )
                return cell
                
            case LandingScreenTVSections.headerFaqsSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "HeaderFaqsTVC") as! HeaderFaqsTVC
                cell.confingerCellLanding(vc: self.controller)
                return cell
                
            case LandingScreenTVSections.faqSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: LandingFAQTVC.identifier) as! LandingFAQTVC
                cell.configureCell(faq: self.landingSellList.lstFAQObject?[indexPath.row])
                return cell
                
            case LandingScreenTVSections.needHelpSec.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: LandingNeedHelpSectionTVC.identifier) as! LandingNeedHelpSectionTVC
                cell.confingerCellWith(vc: self.controller, trigger: "sell_car_landing", isPackagesScreen: false, isCallBackApiAlreadyCalled: false)
                return cell
                
            default:
                return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
            case LandingScreenTVSections.howSellingWorksSec.rawValue,
                LandingScreenTVSections.animatedBannersSec.rawValue,
                LandingScreenTVSections.staticBadgesHeaderSec.rawValue,
                LandingScreenTVSections.staticBadgesHeaderTwoSec.rawValue,
                LandingScreenTVSections.abandonedSec.rawValue,
                LandingScreenTVSections.dealersSec.rawValue,
                LandingScreenTVSections.bundleSec.rawValue,
                LandingScreenTVSections.needHelpSec.rawValue,
                LandingScreenTVSections.headerFaqsSec.rawValue,
                LandingScreenTVSections.faqSec.rawValue:
                return UITableView.automaticDimension
                
            case LandingScreenTVSections.sellCarSec.rawValue:
                return LanguageHelper.language.currentLanguage() == "ar" ? 90 : 80
//            ? (self.landingSellList.priceSellYourCarObject?.title ?? "" == "" || self.landingSellList.priceSellYourCarObject?.title ?? "" == nil  ? 90 : 120)
//                    : (self.landingSellList.priceSellYourCarObject?.title ?? "" == "" || self.landingSellList.priceSellYourCarObject?.title ?? "" == nil
//                       ? 80 : 140)
                
            case LandingScreenTVSections.customerReviewSec.rawValue:
                return 286
                
            case LandingScreenTVSections.togglePackagesSec.rawValue:
                return ConstantsValues.sharedInstance.isSelfSellingCar ? 57 : 0
                
            default:
                return 0
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch indexPath.section {
            
            case LandingScreenTVSections.bundleSec.rawValue:
                didTapOnBundles()
                
            
            case LandingScreenTVSections.dealersSec.rawValue:
                moveToDealerWebSite()
                #if DEVELOPMENT
                #else
                Analytics.logEvent("dealer_join_us", parameters: ["trigger" : "sell_car"])
                #endif
                
            case LandingScreenTVSections.faqSec.rawValue:
                guard var faq = self.landingSellList.lstFAQObject?[indexPath.row] else { return }
                faq.isOpen = !faq.isOpen
                self.landingSellList.lstFAQObject?[indexPath.row] = faq
                DispatchQueue.main.async {
                    UIView.performWithoutAnimation {
                        self.tv.reloadData()
                    }
                }
                
            default:
                break
        }
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
//        if indexPath.section == LandingScreenTVSections.sellCarSec.rawValue || indexPath.section == LandingScreenTVSections.abandonedSec.rawValue {
//            self.headerCanMove = false
//            setSectionFooter(seeSection: true)
//        } else if headerCanMove {
//            setSectionFooter(seeSection: false)
//        }
        if indexPath.section == LandingScreenTVSections.sellCarSec.rawValue || indexPath.section == LandingScreenTVSections.abandonedSec.rawValue {
            self.headerCanMove = false
            setSectionFooter(seeSection: true)
        } else if headerCanMove {
            setSectionFooter(seeSection: true)
        }
    }
    
    func tableView(_ tableView: UITableView, didEndDisplaying cell: UITableViewCell, forRowAt indexPath: IndexPath) {
//        if indexPath.section == LandingScreenTVSections.sellCarSec.rawValue || indexPath.section == LandingScreenTVSections.abandonedSec.rawValue {
//            headerCanMove = true
//            setSectionFooter(seeSection: true)
//        } else if headerCanMove {
//            setSectionFooter(seeSection: false)
//        }
        if indexPath.section == LandingScreenTVSections.sellCarSec.rawValue || indexPath.section == LandingScreenTVSections.abandonedSec.rawValue {
            headerCanMove = true
            setSectionFooter(seeSection: true)
        } else if headerCanMove {
            setSectionFooter(seeSection: true)
        }
    }
    
    func setSectionFooter(seeSection: Bool) {
        self.landingVC?.containerView.isHidden = seeSection
        if seeSection == true {
            self.landingVC?.containerViewCons.constant = 0
            self.landingVC?.tableViewBottomCons.constant = 20
            self.landingVC?.containerView.isHidden = true
        } else {
            self.landingVC?.containerViewCons.constant = 88
            self.landingVC?.tableViewBottomCons.constant = 90
            self.landingVC?.containerView.isHidden = false
        }
    }
    
    internal func buttonClicked(_ tag: Int) {
        var faq = faqList[tag]
        faq.isOpen = faq.isOpen ? false : true
        self.faqList[tag] = faq
        let indexPath = IndexPath(item: tag, section: LandingScreenTVSections.faqSec.rawValue)
        self.tv.reloadRows(at: [indexPath], with: .none)
    }
    
//    func scrollViewDidScroll(_ scrollView: UIScrollView) {
//        guard let tabBar = self.controller?.tabBarController?.tabBar else { return }
//        
//        let translation = scrollView.panGestureRecognizer.translation(in: scrollView)
//        
//        UIView.animate(withDuration: 0.3) {
//            if translation.y > 0 {
//                tabBar.frame.origin.y = self.controller?.view.frame.height ?? 0 - tabBar.frame.height
//            } else if translation.y < 0 {
//                tabBar.frame.origin.y = self.controller?.view.frame.height ?? 0
//            }
//        }
//    }
    
    private func didTapOnBundles() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if UserHelper.user.isLogin() {
                let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
                
                if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                    let validationVC = self.landingVC?.getNextViewController(
                        viewControllerClass: MobileNumberValidationVC.self,
                        storyBoardName: "Authentication",
                        identifier: "MobileNumberValidationVC"
                    ) ?? MobileNumberValidationVC()
                    
                    validationVC.modalPresentationStyle = .custom
                    
                    self.landingVC?.present(validationVC, animated: true)
                } else {
                    guard let navigationController = self.landingVC?.navigationController else { return }
                    SellingMultipleRouter.build(from: navigationController)
                    ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
                    ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId = 0
                }
            } else {
                self.landingVC?.moveToLogin()
            }
        }
    }
    
}

extension LandingSellCarTVHandler: SellCarInfoCTVProtocol {
    func moveToModelList(brandId: Int) {
        self.delegate?.moveToModelList(brandId: brandId)
    }
    
    func startSellingProcess() {
        self.delegate?.editAbandonedAction()
    }
}
