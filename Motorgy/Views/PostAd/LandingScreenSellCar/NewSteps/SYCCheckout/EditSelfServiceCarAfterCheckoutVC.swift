//
//  EditSelfServiceCarAfterCheckoutVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 01/06/2024.
//  Copyright © 2024 <PERSON><PERSON>z. All rights reserved.
//

import UIKit

class EditSelfServiceCarAfterCheckoutVC: BaseVC {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var continueButtonView: SellingProcessCustomButtonView!
    
    private var myCarDetailsVM = MyCarDetailsViewModel()
    private var carDetailsList: [(String, Any)] = []
    private var userData: [String: Any] = [:]
    private var userSelectedNewData: [Int: Any] = [:]
    private var uploadedImages: [UploadImageItemModel] = []
    private var cachedPackagesData: [LstPackages] = []
    private var newPackageData: [LstPackages] = []
    private var adIdToPostApi = 0
    private var selfServiceAdDetails = SelfServiceAdDetails(brandId: nil, brandName: nil, modelId: nil, modelName: nil, trimId: nil, trimName: nil, makeYear: nil, mileage: nil, estimatedPrice: nil, packageId: nil, packageName: nil, addonIds: nil, addons: nil, transmissionId: nil, engineCylinderId: nil, insuranceTypeId: nil, inspectionFile: nil, paintId: nil, paintLocations: nil, carBodyDamageAnswer: nil, carBodyDamageLocations: nil, chassisIssuesAnswer: nil, chassisIssuesIds: nil, mechanicalIssuesAnswer: nil, mechanicalIssuesIds: nil, dashboardIssuesAnswer: nil, dashboardIssuesIds: nil, tireConditionId: nil, exteriorColorId: nil, interiorColorId: nil, extraFeatureIds: nil, lstImages: nil, isSelfService: nil, inspectionFileName: nil, additionalInfo: nil, uploadedInspectionFile: nil, uploadedImages: [])
    private var isTrim = false
    private var selectedPakcage: LstPackages?
    private var transmissionsArray: [LstTransmissions] = []
    private var enginesArray: [LstEngineCylinders] = []
    private var insuranceArray: [LstInsuranceTypes] = []
    private var paintsArray: [LstPaints] = []
    private var tiresArray: [LstTireCondition] = []
    private var exteriorColorsArray: [LstExteriorColors] = []
    private var interiorColorsArray: [LstInteriorColors] = []
	private var isOpenFromMicroDealerListCarFlow: Bool = false
	var popPackForMicroDealerListFlowContinue: (([String: Any], [Int: Any], Bool) -> Void)?
    
    override func viewDidLoad() {
        super.viewDidLoad()

        self.title = "Edit car details".localized
        self.navigationItem.backButtonTitle = ""

        self.getData()
        self.setupNotificationCenterObservers()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // Ensure back button text is always hidden when returning to this screen
        self.navigationItem.backButtonTitle = ""
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
	func setMicroDealerListCarFlow(_ isOpenFromMicroDealerListCarFlow: Bool) {
		self.isOpenFromMicroDealerListCarFlow = isOpenFromMicroDealerListCarFlow
	}
	
    private func setupNotificationCenterObservers() {
        NotificationCenter.default.addObserver(self, selector: #selector(getSelectedBoyPaintedBackFromPaintVisualScreen(_:)), name: .paintedPartsVisualFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getSelectedDamagePartsBackFromPaintVisualScreen(_:)), name: .damagePartsVisualFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getSelectedDashLightIssues(_:)), name: .dashLightIssuesFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getSelectedChassieIssues(_:)), name: .chassieIssuesFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getSelectedMechanicalIssues(_:)), name: .mechanicalIssuesFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getInspectionReportFile(_:)), name: .inspectionFileSelfServiceFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getMakeChange(_:)), name: .makeAttributeChangeSelfServiceFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getModelChange(_:)), name: .modelAttributeChangeSelfServiceFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getTrimChange(_:)), name: .trimAttributeChangeSelfServiceFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(getYearChange(_:)), name: .yearAttributeChangeSelfServiceFromEditAfterCheckout, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(removeTrimData(_:)), name: .removeTrimIfNotExistFromSelfSellingCarData, object: nil)
    }
    
    private func setupUI() {
        self.view.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        
        self.tableView.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.register(SYCEditDetailsTVC.nib(), forCellReuseIdentifier: SYCEditDetailsTVC.identifier)
        self.tableView.estimatedRowHeight = UITableView.automaticDimension
        self.tableView.separatorStyle = .none
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.isScrollEnabled = true
        self.tableView.contentInset = UIEdgeInsets(top: 24, left: 0, bottom: 100, right: 0)
        self.tableView.reloadData()
        
        self.continueButtonView.isHidden = false
		
		self.continueButtonView.configureContinue(buttonTitle: "Continue".localized) {
			if self.isOpenFromMicroDealerListCarFlow {
				self.popPackForMicroDealerListFlowContinue?(self.userData, self.userSelectedNewData, self.isTrim)
				self.navigationController?.popViewController(animated: true)
				self.isOpenFromMicroDealerListCarFlow = false
			} else {
				let modelId = (self.userData["1"] as? DataAttribute)?.id ?? 0
				let year = (self.userData["\(self.isTrim ? 3 : 2)"] as? DataAttribute)?.id  ?? 0
				let mileage = (self.userData["\(self.isTrim ? 4 : 3)"] as? DataAttribute)?.id  ?? 0
				
				let isSelfSellingCar = SelfServiceDataSource.shared.getIsSelectedRouteSelected()
				
				self.myCarDetailsVM.getPackageDetails(modelId: modelId, year: year, mileage: mileage, isSelfSellingCar: isSelfSellingCar, adId: nil, isFromUpgrade: nil, estimatedPrice: nil).bind { [weak self] packages in
					DispatchQueue.main.async {
						if self?.cachedPackagesData == packages {
							self?.goToCheckoutScreen()
						} else {
							self?.newPackageData = packages ?? []
							self?.presentPackagesUpdatedActionSheet()
						}
					}
				}.disposed(by: self.myCarDetailsVM.getDisposeBag())
			}
		}
    }
    
    private func getData() {
        let carBodyDamageLocations = (self.userSelectedNewData[SelfSellingScreensType.bodyDamageVisual.rawValue] as? [SelectedSelfSellingData])?.first?.customBodyParts?.map {
            CarBodyDamageLocations(damageId: $0.damageId, damageLocation: $0.damageLocation.rawValue, note: $0.note)
        }
        
        self.selfServiceAdDetails.brandId = (self.userData["0"] as? DataAttribute)?.id ?? 0
        self.selfServiceAdDetails.brandName = (self.userData["0"] as? DataAttribute)?.name ?? ""
        self.selfServiceAdDetails.modelId = (self.userData["1"] as? DataAttribute)?.id
        self.selfServiceAdDetails.modelName = (self.userData["1"] as? DataAttribute)?.name
        self.selfServiceAdDetails.trimId = (self.userData["2"] as? DataAttribute)?.id
        self.selfServiceAdDetails.trimName = (self.userData["2"] as? DataAttribute)?.name
        self.selfServiceAdDetails.makeYear = (self.userData[self.isTrim ? "3" : "2"] as? DataAttribute)?.id
        self.selfServiceAdDetails.mileage = Double((self.userData[self.isTrim ? "4" : "3"] as? DataAttribute)?.id ?? 0)
        self.selfServiceAdDetails.estimatedPrice = Double((self.userData[self.isTrim ? "5" : "4"] as? DataAttribute)?.id ?? 0)
        self.selfServiceAdDetails.packageId = self.selectedPakcage?.packageId ?? 0
        self.selfServiceAdDetails.packageName = self.selectedPakcage?.packageName ?? ""
        self.selfServiceAdDetails.addonIds = nil
        self.selfServiceAdDetails.addons = nil
        self.selfServiceAdDetails.transmissionId = (self.userSelectedNewData[SelfSellingScreensType.transmission.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.engineCylinderId = (self.userSelectedNewData[SelfSellingScreensType.engine.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.insuranceTypeId = (self.userSelectedNewData[SelfSellingScreensType.warranty.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.inspectionFile = nil
        self.selfServiceAdDetails.paintId = (self.userSelectedNewData[SelfSellingScreensType.paintCondition.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.paintLocations = (self.userSelectedNewData[SelfSellingScreensType.paintVisual.rawValue] as? [SelectedSelfSellingData])?.first?.ids
        self.selfServiceAdDetails.carBodyDamageAnswer = (self.userSelectedNewData[SelfSellingScreensType.anyBodyDamage.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.carBodyDamageLocations = carBodyDamageLocations
        self.selfServiceAdDetails.chassisIssuesAnswer = (self.userSelectedNewData[SelfSellingScreensType.anyChassieIssues.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.chassisIssuesIds = (self.userSelectedNewData[SelfSellingScreensType.chassieIssues.rawValue] as? [SelectedSelfSellingData])?.first?.ids
        self.selfServiceAdDetails.mechanicalIssuesAnswer = (self.userSelectedNewData[SelfSellingScreensType.anyMechanicalIssues.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.mechanicalIssuesIds = (self.userSelectedNewData[SelfSellingScreensType.mechanicalIssues.rawValue] as? [SelectedSelfSellingData])?.first?.ids
        self.selfServiceAdDetails.dashboardIssuesAnswer = (self.userSelectedNewData[SelfSellingScreensType.anyDashLightIssues.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.dashboardIssuesIds = (self.userSelectedNewData[SelfSellingScreensType.dashLightIssues.rawValue] as? [SelectedSelfSellingData])?.first?.ids
        self.selfServiceAdDetails.tireConditionId = (self.userSelectedNewData[SelfSellingScreensType.tire.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.exteriorColorId = (self.userSelectedNewData[SelfSellingScreensType.exteriorColor.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.interiorColorId = (self.userSelectedNewData[SelfSellingScreensType.interiorColor.rawValue] as? [SelectedSelfSellingData])?.first?.id
        self.selfServiceAdDetails.extraFeatureIds = (self.userSelectedNewData[SelfSellingScreensType.features.rawValue] as? [SelectedSelfSellingData])?.first?.ids
        self.selfServiceAdDetails.lstImages = nil
        self.selfServiceAdDetails.isSelfService = true
        self.selfServiceAdDetails.inspectionFileName = (self.userSelectedNewData[SelfSellingScreensType.uploadInspectionReport.rawValue] as? [SelectedSelfSellingData])?.first?.reportFile?.name
        self.selfServiceAdDetails.additionalInfo = (self.userSelectedNewData[SelfSellingScreensType.featuresAdditionalNote.rawValue] as? [SelectedSelfSellingData])?.first?.additionalInfo
        self.selfServiceAdDetails.uploadedInspectionFile = (self.userSelectedNewData[SelfSellingScreensType.uploadInspectionReport.rawValue] as? [SelectedSelfSellingData])?.first?.reportFile
        self.selfServiceAdDetails.uploadedImages = self.uploadedImages.filter { $0.uploadedImage != nil }
        
        self.transmissionsArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.transmission.rawValue) as? [LstTransmissions] ?? []
        self.enginesArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.engine.rawValue) as? [LstEngineCylinders] ?? []
        self.insuranceArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.warranty.rawValue) as? [LstInsuranceTypes] ?? []
        self.paintsArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.paintCondition.rawValue) as? [LstPaints] ?? []
        self.tiresArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.tire.rawValue) as? [LstTireCondition] ?? []
        self.exteriorColorsArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.exteriorColor.rawValue) as? [LstExteriorColors] ?? []
        self.interiorColorsArray = SelfServiceDataSource.shared.getArray(forIndex: SelfSellingScreensType.interiorColor.rawValue) as? [LstInteriorColors] ?? []
        
        let photosWord = self.selfServiceAdDetails.uploadedImages.count > 1 ? "photos".localized : "photo".localized
        let inspectionFileName = self.selfServiceAdDetails.inspectionFileName != nil && self.selfServiceAdDetails.inspectionFileName != "" ? self.selfServiceAdDetails.inspectionFileName : "No".localized
        
        self.addField("Make".localized, value: self.selfServiceAdDetails.brandName)
        self.addField("Model".localized, value: self.selfServiceAdDetails.modelName)
        if self.isTrim {
            self.addField("Trim".localized, value: self.selfServiceAdDetails.trimName)
        }
        self.addField("Year".localized, value: self.selfServiceAdDetails.makeYear)
        self.addField("Mileage".localized, value: "\((self.selfServiceAdDetails.mileage ?? 0).withCommas()) " + "KM".localized)
        self.addField("Price".localized, value: "\((self.selfServiceAdDetails.estimatedPrice ?? 0).withCommas()) " + "KWD".localized)
        if self.selfServiceAdDetails.packageId != 19 {
            self.addField("Car photos".localized, value: "\(self.selfServiceAdDetails.uploadedImages.count) " + photosWord)
        }
        self.addField("Transmission".localized, value: self.myCarDetailsVM.getTransmissionName(by: self.selfServiceAdDetails.transmissionId ?? 0, from: self.transmissionsArray))
        self.addField("Engine".localized, value: self.myCarDetailsVM.getEngineCylinderName(by: self.selfServiceAdDetails.engineCylinderId ?? 0, from: self.enginesArray))
        self.addField("Warranty".localized, value: self.myCarDetailsVM.getInsuranceTypeName(by: self.selfServiceAdDetails.insuranceTypeId ?? 0, from: self.insuranceArray))
        self.addField("Inspection report".localized, value: inspectionFileName)
        self.addField("Paint condition".localized, value: self.myCarDetailsVM.getPaintConditionName(by: self.selfServiceAdDetails.paintId ?? 0, from: self.paintsArray))
        self.addField("Body panels issues".localized, value: self.myCarDetailsVM.getAnyAnswerText(for: self.selfServiceAdDetails.carBodyDamageAnswer))
        self.addField("Mechanical issues".localized, value: self.myCarDetailsVM.getAnyAnswerText(for: self.selfServiceAdDetails.mechanicalIssuesAnswer))
        self.addField("Dashboard warning lights".localized, value: self.myCarDetailsVM.getAnyAnswerText(for: self.selfServiceAdDetails.dashboardIssuesAnswer))
        self.addField("Chassis issues".localized, value: self.myCarDetailsVM.getAnyAnswerText(for: self.selfServiceAdDetails.chassisIssuesAnswer))
        self.addField("Tires condition".localized, value: self.myCarDetailsVM.getTireConditionName(by: self.selfServiceAdDetails.tireConditionId ?? 0, from: self.tiresArray))
        let exteriorColorName = self.myCarDetailsVM.getExteriorColorName(by: self.selfServiceAdDetails.exteriorColorId ?? 0, from: self.exteriorColorsArray)
        let interiorColorName = self.myCarDetailsVM.getInteriorColorName(by: self.selfServiceAdDetails.interiorColorId ?? 0, from: self.interiorColorsArray)
        if exteriorColorName == "" && interiorColorName == "" {
            self.addField("Exterior & Interior color".localized, value: "No".localized)
        } else if exteriorColorName == "" {
            self.addField("Exterior & Interior color".localized, value: "\(interiorColorName)")
        } else if interiorColorName == "" {
            self.addField("Exterior & Interior color".localized, value: "\(exteriorColorName)")
        } else {
            self.addField("Exterior & Interior color".localized, value: "\(exteriorColorName), \(interiorColorName)")
        }
        self.addField("Features".localized, value: self.selfServiceAdDetails.extraFeatureIds?.count ?? 0 > 0 ? "Yes".localized : "No".localized)
        
        self.setupUI()
    }
    
    private func addField(_ name: String, value: Any?) {
        let fieldValue: Any
        
        if let value = value {
            if let stringValue = value as? String, stringValue.isEmpty {
                fieldValue = "No".localized
            } else {
                fieldValue = value
            }
        } else {
            fieldValue = "No".localized
        }
        
        self.carDetailsList.append((name, fieldValue))
    }
    
    private func presentPackagesUpdatedActionSheet() {
        let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
        timeSlotNotAvailableVC.modalPresentationStyle = .custom
        timeSlotNotAvailableVC.setData(controller: self, screenType: .packageUpdated)
        timeSlotNotAvailableVC.packagesDelegate = self
        self.present(timeSlotNotAvailableVC, animated: true)
    }
    
    private func goToCheckoutScreen() {
        if let navigationController = self.navigationController {
            for vc in navigationController.viewControllers.reversed() {
                if let checkoutViewController = vc as? SYCProcessCheckoutVC {
                    checkoutViewController.updateDataForSelfService(uploadedImages: self.uploadedImages, selfSellingUserData: self.userData, userSelectedNewData: self.userSelectedNewData, isTrim: self.isTrim)
                    navigationController.popToViewController(checkoutViewController, animated: true)
                    break
                }
            }
        }
    }
    
    public func setUserDataForSelfService(uploadedImages: [UploadImageItemModel]?, selfSellingUserData: [String: Any]?, userSelectedNewData: [Int: Any]?, selectedPakcage: LstPackages?, isTrim: Bool, cachedPackagesData: [LstPackages], adIdToPostApi: Int? = nil) {
        self.userData = selfSellingUserData ?? [:]
        self.uploadedImages = uploadedImages ?? []
        self.userSelectedNewData = userSelectedNewData ?? [:]
        self.selectedPakcage = selectedPakcage
        self.isTrim = isTrim
        self.adIdToPostApi = adIdToPostApi ?? 0
        self.cachedPackagesData = cachedPackagesData
    }
}

extension EditSelfServiceCarAfterCheckoutVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.carDetailsList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: SYCEditDetailsTVC.identifier, for: indexPath) as? SYCEditDetailsTVC
        cell?.selectionStyle = .none
        cell?.backgroundColor = .clear
        
        let (fieldName, value) = self.carDetailsList[indexPath.row]
        
        cell?.configureCell(key: fieldName, value: "\(value)", type: .selfServiceEdit, isOpenFromEditAfterCheckout: true)
        
        return cell ?? UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let (fieldName, _) = self.carDetailsList[indexPath.row]
        var type: String?, data: [Any]?
        
        switch fieldName {
            case "Make".localized:
                type = BrandModelResult.shared.self?.type
                data = BrandModelResult.shared.self?.lstBrandModelYear
                self.moveToCarDetails(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
                
            case "Model".localized:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({$0.brandID == brandId})
                type = brand?[0].lstModels?[0].type
                data = brand?[0].lstModels
                self.moveToCarDetails(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
                
            case "Trim".localized:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let modelId = (self.userData["1"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({ $0.brandID == brandId })
                let model = brand?[0].lstModels?.filter({ $0.modelID == modelId })
                if (model?[0].lstTrims?.count ?? 0) > 1 {
                    type = "Trim".localized
                    data = model?[0].lstTrims
                } else {
                    type = model?[0].nextType
                    data = model?[0].lstYear
                }
                self.moveToCarDetails(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
                
            case "Year".localized:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({$0.brandID == brandId})
                let modelId = (self.userData["1"] as? DataAttribute)?.id
                let model = brand?[0].lstModels?.filter({$0.modelID == modelId})
                type = model?[0].nextType
                data = model?[0].lstYear
                self.moveToCarDetails(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
                
            case "Mileage".localized: self.openMileageRow()
            case "Price".localized:  self.openPriceRow()
            case "Car photos".localized: self.openPhotosRow()
            case "Transmission".localized: self.openTransmissionRow()
            case "Engine".localized: self.openEngineRow()
            case "Warranty".localized: self.openWarrantyRow()
            case "Paint condition".localized: self.openPaintRow()
            case "Body panels issues".localized: self.openBodyDamageRow()
            case "Mechanical issues".localized: self.openMechanicalRow()
            case "Dashboard warning lights".localized: self.openDashlightRow()
            case "Chassis issues".localized: self.openChassieRow()
            case "Tires condition".localized: self.openTireRow()
            case "Exterior & Interior color".localized: self.openMergedColorsScreen()
            case "Features".localized: self.openFeaturesRow()
                
            case "Inspection report".localized:
                if #available(iOS 14, *) {
                    self.openInspectionFileRow()
                }
                
            default:
                break
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
}

extension EditSelfServiceCarAfterCheckoutVC {
    private func moveToCarDetails(isFirst: Bool, type: String?, data: [Any]?, isEdit: Bool, canEdit: Bool, isTrim: Bool) {
        let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self, storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
        carDetailsVC.setData(
            brandModelResult: BrandModelResult.shared.self,
            onDismiss: nil,
            isFirst: isFirst,
            type: type,
            data: data,
            userData: userData,
            serviceId: 0,
            isEdit: isEdit,
            canEdit: canEdit,
            isTrim: isTrim
        )
        carDetailsVC.setIsOpenFromEditDashboardForSelfServiceCar(value: true, openFromEditAfterCheckoutForSelfServiceCar: true)
        self.navigationController?.pushViewController(carDetailsVC, animated: true)
    }
    
    private func openMechanicalRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.anyMechanicalIssues.rawValue)
        completeCarDetailsVC.setAnyMechanicalIssuesAnswerFromEditScreen(value: self.selfServiceAdDetails.mechanicalIssuesAnswer, isOpenFromEditDashboard: false, selectedMechanicalIssues: self.selfServiceAdDetails.mechanicalIssuesIds, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        
        if self.selfServiceAdDetails.mechanicalIssuesAnswer == 1 {
            let multiSelectScreenVC = self.getNextViewController(viewControllerClass: SelfSellingMultiSelectVC.self, storyBoardName: "SelfService", identifier: "SelfSellingMultiSelectVC") ?? SelfSellingMultiSelectVC()
            multiSelectScreenVC.setDataForEditFromAfterCheckout(currentIndex: SelfSellingScreensType.mechanicalIssues.rawValue, selectedIds: self.selfServiceAdDetails.mechanicalIssuesIds, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(multiSelectScreenVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openChassieRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.anyChassieIssues.rawValue)
        completeCarDetailsVC.setAnyChassieIssuesAnswerFromEditScreen(value: self.selfServiceAdDetails.chassisIssuesAnswer, isOpenFromEditDashboard: false, selectedChassieIssues: self.selfServiceAdDetails.chassisIssuesIds, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        
        if self.selfServiceAdDetails.chassisIssuesAnswer == 1 {
            let multiSelectScreenVC = self.getNextViewController(viewControllerClass: SelfSellingMultiSelectVC.self, storyBoardName: "SelfService", identifier: "SelfSellingMultiSelectVC") ?? SelfSellingMultiSelectVC()
            multiSelectScreenVC.setDataForEditFromAfterCheckout(currentIndex: SelfSellingScreensType.chassieIssues.rawValue, selectedIds: self.selfServiceAdDetails.chassisIssuesIds, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(multiSelectScreenVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openDashlightRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.anyDashLightIssues.rawValue)
        completeCarDetailsVC.setAnyDashLightIssuesAnswerFromEditScreen(value: self.selfServiceAdDetails.dashboardIssuesAnswer, selectedDashLightIssues: self.selfServiceAdDetails.dashboardIssuesIds, isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        
        if self.self.selfServiceAdDetails.dashboardIssuesAnswer == 1 {
            let dashLightIssuesVC = self.getNextViewController(viewControllerClass: DashboardLightIssuesVC.self, storyBoardName: "SelfService", identifier: "DashboardLightIssuesVC") ?? DashboardLightIssuesVC()
            dashLightIssuesVC.setDataForEditFromAfterCheckout(currentIndex: SelfSellingScreensType.dashLightIssues.rawValue, selectedIds: self.selfServiceAdDetails.dashboardIssuesIds, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(dashLightIssuesVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openTireRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.tire.rawValue)
        completeCarDetailsVC.setTireIdFromEditScreen(id: self.selfServiceAdDetails.tireConditionId, isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
    }
    
    private func openMergedColorsScreen() {
        let mergedColorsVC = self.getNextViewController(viewControllerClass: InteriorExteriorMergedColorsScreenVC.self, storyBoardName: "SelfService", identifier: "InteriorExteriorMergedColorsScreenVC") ?? InteriorExteriorMergedColorsScreenVC()
        mergedColorsVC.setExteriorAndInteriorColorsFromEditAfterCheckout(
            exteriorColorId: self.selfServiceAdDetails.exteriorColorId,
            controller: nil,
            checkoutController: self,
            openFromEditAfterCheckout: true,
            interiorColorId: self.selfServiceAdDetails.interiorColorId
        )
        self.navigationController?.pushViewController(mergedColorsVC, animated: true)
    }
    
    private func openTransmissionRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.transmission.rawValue)
        completeCarDetailsVC.setTransmissionIdFromEditScreen(id: self.selfServiceAdDetails.transmissionId, isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
    }
    
    private func openEngineRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.engine.rawValue)
        completeCarDetailsVC.setEngineIdFromEditScreen(id: self.selfServiceAdDetails.engineCylinderId, isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
    }
    
    private func openWarrantyRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.warranty.rawValue)
        completeCarDetailsVC.setInsurrenceIdFromEditScreen(id: self.selfServiceAdDetails.insuranceTypeId, isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
    }
    
    private func openMileageRow() {
        let kmVC = self.getNextViewController(viewControllerClass: KMVC.self, storyBoardName: "SellYouCar", identifier: "KMVC") ?? KMVC()
        kmVC.setMileageFromEditScreen(kmValue: Int(self.selfServiceAdDetails.mileage ?? 0), isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(kmVC, animated: true)
    }
    
    private func openPriceRow() {
        let priceRangeVC = self.getNextViewController(viewControllerClass: PriceRangeVC.self, storyBoardName: "SellYouCar", identifier: "PriceRangeVC") ?? PriceRangeVC()
        priceRangeVC.setPriceFromEditScreen(priceValue: Int(self.selfServiceAdDetails.estimatedPrice ?? 0), isOpenFromEditDashboard: false, controller: nil, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(priceRangeVC, animated: true)
    }
    
    private func openPhotosRow() {
        let uploadImagesVC = self.getNextViewController(viewControllerClass: SCUploadPhotosVC.self, storyBoardName: "SelfService", identifier: "SCUploadPhotosVC") ?? SCUploadPhotosVC()
        uploadImagesVC.setUploadedPhotosFromEditScreen(
            uploadedImagesFromEditScreen: self.uploadedImages,
            isOpenFromEditDashboard: false,
            controller: nil,
            checkoutController: self,
            openFromEditAfterCheckout: true,
            isConfirmedByModerators: false
        )
        self.navigationController?.pushViewController(uploadImagesVC, animated: true)
    }
    
    @available(iOS 14, *)
    private func openInspectionFileRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.haveInspectionReport.rawValue)
        var hasInspectionFile = 0
        if let inspectionFileName = self.selfServiceAdDetails.inspectionFileName, inspectionFileName != "" {
            hasInspectionFile = 1 // yes
        } else {
            hasInspectionFile = 2 // no
        }
        completeCarDetailsVC.setHasInspectionFileAnswerFromEditScreen(value: hasInspectionFile, isOpenFromEditDashboard: false, controller: nil, file: self.selfServiceAdDetails.uploadedInspectionFile, checkoutController: self, openFromEditAfterCheckout: true)
        
        if hasInspectionFile == 1 {
            let uploadInspectionReportVC = self.getNextViewController(viewControllerClass: SCUploadInspectionReportVC.self, storyBoardName: "SelfService", identifier: "SCUploadInspectionReportVC") ?? SCUploadInspectionReportVC()
            uploadInspectionReportVC.setCurrentIndex(currentIndex: SelfSellingScreensType.uploadInspectionReport.rawValue)
            uploadInspectionReportVC.setDataForEditFromAfterCheckout(uploadedFile: self.selfServiceAdDetails.uploadedInspectionFile, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(uploadInspectionReportVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openPaintRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.paintCondition.rawValue)
        completeCarDetailsVC.setPaintIdFromEditScreen(id: self.selfServiceAdDetails.paintId, isOpenFromEditDashboard: false, controller: nil, selectedPaintLocations: self.selfServiceAdDetails.paintLocations, checkoutController: self, openFromEditAfterCheckout: true)
        
        if self.selfServiceAdDetails.paintId == 1004 {
            let paintVisualVC = self.getNextViewController(viewControllerClass: PaintVisualVC.self, storyBoardName: "SelfService", identifier: "PaintVisualVC") ?? PaintVisualVC()
            paintVisualVC.setDataForEditFromAfterCheckout(currentIndex: SelfSellingScreensType.paintVisual.rawValue, selectedIds: self.selfServiceAdDetails.paintLocations, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(paintVisualVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openBodyDamageRow() {
        let completeCarDetailsVC = self.getNextViewController(viewControllerClass: SCCompleteCarDetailsVC.self, storyBoardName: "SelfService", identifier: "SCCompleteCarDetailsVC") ?? SCCompleteCarDetailsVC()
        completeCarDetailsVC.setCurrentIndex(currentIndex: SelfSellingScreensType.anyBodyDamage.rawValue)
        completeCarDetailsVC.setAnyDamageAnswerFromEditScreen(value: self.selfServiceAdDetails.carBodyDamageAnswer, isOpenFromEditDashboard: false, controller: nil, selectedBodyDamageLocations: self.selfServiceAdDetails.carBodyDamageLocations, checkoutController: self, openFromEditAfterCheckout: true)
        
        if self.selfServiceAdDetails.carBodyDamageAnswer == 1 {
            let bodyDamageVisualVC = self.getNextViewController(viewControllerClass: BodyDamageVisualVC.self, storyBoardName: "SelfService", identifier: "BodyDamageVisualVC") ?? BodyDamageVisualVC()
            bodyDamageVisualVC.setDataForEditFromAfterCheckout(currentIndex: SelfSellingScreensType.bodyDamageVisual.rawValue, bodyDamageLocations: self.selfServiceAdDetails.carBodyDamageLocations, openFromEditAfterCheckout: true)
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: false)
            self.navigationController?.pushViewController(bodyDamageVisualVC, animated: true)
        } else {
            self.navigationController?.pushViewController(completeCarDetailsVC, animated: true)
        }
    }
    
    private func openFeaturesRow() {
        let featuresVC = self.getNextViewController(viewControllerClass: SelfServiceFeaturesVC.self, storyBoardName: "SelfService", identifier: "SelfServiceFeaturesVC") ?? SelfServiceFeaturesVC()
        featuresVC.setDataForEditFromAfterCheckout(selectedFeaturesIds: self.selfServiceAdDetails.extraFeatureIds, controller: nil, additionalNote: self.selfServiceAdDetails.additionalInfo, checkoutController: self, openFromEditAfterCheckout: true)
        self.navigationController?.pushViewController(featuresVC, animated: true)
    }
}

extension EditSelfServiceCarAfterCheckoutVC: EditSelfServiceCarDetailsProtocol {
    func sendNewTransmissionDataBack(transmissionId: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Transmission".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getTransmissionName(by: transmissionId, from: self.transmissionsArray)
            self.selfServiceAdDetails.transmissionId = transmissionId
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.transmission.rawValue] = [
            SelectedSelfSellingData(name: "TransmissionId", id: transmissionId)
        ]
    }
    
    func sendNewEngineDataBack(engineId: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Engine".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getEngineCylinderName(by: engineId, from: self.enginesArray)
            self.selfServiceAdDetails.engineCylinderId = engineId
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.engine.rawValue] = [
            SelectedSelfSellingData(name: "EngineCylinderId", id: engineId)
        ]
    }
    
    func sendNewInsuranceDataBack(insuranceId: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Warranty".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getInsuranceTypeName(by: insuranceId, from: self.insuranceArray)
            self.selfServiceAdDetails.insuranceTypeId = insuranceId
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.warranty.rawValue] = [
            SelectedSelfSellingData(name: "InsuranceTypeId", id: insuranceId)
        ]
    }
    
    func sendNewNotPartialPaintConditionDataBack(paintId: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Paint condition".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getPaintConditionName(by: paintId, from: self.paintsArray)
            self.selfServiceAdDetails.paintId = paintId
            self.selfServiceAdDetails.paintLocations = []
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.paintCondition.rawValue] = [
            SelectedSelfSellingData(name: "PaintId", id: paintId)
        ]
    }
    
    func sendNewTireConditionDataBack(tireId: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Tires condition".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getTireConditionName(by: tireId, from: self.tiresArray)
            self.selfServiceAdDetails.tireConditionId = tireId
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.tire.rawValue] = [
            SelectedSelfSellingData(name: "TireConditionId", id: tireId)
        ]
    }
    
    func sendNewBodyDamageAnswerDataBack(anyBodyDamageAnswer: AnyDamagesAnswer) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Body panels issues".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: anyBodyDamageAnswer.rawValue)
            self.selfServiceAdDetails.carBodyDamageAnswer = anyBodyDamageAnswer.rawValue
            self.selfServiceAdDetails.carBodyDamageLocations = []
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.anyBodyDamage.rawValue] = [
            SelectedSelfSellingData(name: "CarBodyDamageAnswer", id: anyBodyDamageAnswer.rawValue)
        ]
    }
    
    func sendNewDashLightAnswerDataBack(anyDashLightAnswer: AnyDashboardIssues) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Dashboard warning lights".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: anyDashLightAnswer.rawValue)
            self.selfServiceAdDetails.dashboardIssuesAnswer = anyDashLightAnswer.rawValue
            self.selfServiceAdDetails.dashboardIssuesIds = []
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.anyDashLightIssues.rawValue] = [
            SelectedSelfSellingData(name: "DashboardIssuesAnswer", id: anyDashLightAnswer.rawValue)
        ]
    }
    
    func sendNewAnyChassieAnswerDataBack(anyChassieAnswer: AnyChassieIssues) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Chassis issues".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: anyChassieAnswer.rawValue)
            self.selfServiceAdDetails.chassisIssuesAnswer = anyChassieAnswer.rawValue
            self.selfServiceAdDetails.chassisIssuesIds = []
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.anyChassieIssues.rawValue] = [
            SelectedSelfSellingData(name: "ChassisIssuesAnswer", id: anyChassieAnswer.rawValue)
        ]
    }
    
    func sendNewAnyMechanicalAnswerDataBack(anyMechanicalAnswer: AnyMechanicalIssues) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Mechanical issues".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: anyMechanicalAnswer.rawValue)
            self.selfServiceAdDetails.mechanicalIssuesAnswer = anyMechanicalAnswer.rawValue
            self.selfServiceAdDetails.mechanicalIssuesIds = []
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.anyMechanicalIssues.rawValue] = [
            SelectedSelfSellingData(name: "MechanicalIssuesAnswer", id: anyMechanicalAnswer.rawValue)
        ]
    }
    
    func sendNewMileageData(mileage: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Mileage".localized }) {
            self.carDetailsList[index].1 = "\(mileage.withCommas()) " + "KM".localized
            self.selfServiceAdDetails.mileage = Double(mileage)
            self.tableView.reloadData()
        }
        
        self.userData[isTrim ? "4" : "3"] = DataAttribute(name: "\(mileage) \("KM".localized)", id: mileage, key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
        self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
    }
    
    func sendNewPriceData(price: Int) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Price".localized }) {
            self.carDetailsList[index].1 = "\(price.withCommas()) " + "KWD".localized
            self.selfServiceAdDetails.estimatedPrice = Double(price)
            self.tableView.reloadData()
        }
        
        self.userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: price, key: "Price".localized, dataKey: "EstimatedPrice")
        self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
    }
    
    func sendNewFeaturesTags(selectedIds: [Int], additionalNote: String) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Features".localized }) {
            self.carDetailsList[index].1 = selectedIds.count > 0 ? "Yes".localized : "No".localized
            self.selfServiceAdDetails.extraFeatureIds = selectedIds
            self.selfServiceAdDetails.additionalInfo = additionalNote
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData[SelfSellingScreensType.features.rawValue] = [
            SelectedSelfSellingData(name: "ExtraFeatureIds", id: 0, ids: selectedIds)
        ]
        
        if additionalNote != "" {
            self.userSelectedNewData[SelfSellingScreensType.featuresAdditionalNote.rawValue] = [
                SelectedSelfSellingData(name: "AdditionalInfo", id: 0, additionalInfo: additionalNote)
            ]
        } else {
            self.userSelectedNewData.removeValue(forKey: SelfSellingScreensType.featuresAdditionalNote.rawValue)
        }
    }
    
    func sendNewSelectedExteriorAndInteriorColors(exteriorColor: LstExteriorColors?, interiorColor: LstInteriorColors?) {
        if let exColor = exteriorColor, let inColor = interiorColor {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Exterior & Interior color".localized }) {
                self.carDetailsList[index].1 = "\(exColor.name ?? ""), \(inColor.name ?? "")"
                self.selfServiceAdDetails.exteriorColorId = exColor.id ?? 0
                self.selfServiceAdDetails.interiorColorId = inColor.id ?? 0
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.exteriorColor.rawValue] = [
                SelectedSelfSellingData(name: "ExteriorColorId", id: exColor.id ?? 0)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.interiorColor.rawValue] = [
                SelectedSelfSellingData(name: "InteriorColorId", id: inColor.id ?? 0)
            ]
        } else {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Exterior & Interior color".localized }) {
                self.carDetailsList[index].1 = "No".localized
                self.selfServiceAdDetails.exteriorColorId = nil
                self.selfServiceAdDetails.interiorColorId = nil
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData.removeValue(forKey: SelfSellingScreensType.exteriorColor.rawValue)
            self.userSelectedNewData.removeValue(forKey: SelfSellingScreensType.interiorColor.rawValue)
        }
    }
    
    func sendNewInspectionReportAnswer(answer: InspectionReportAnswer) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Inspection report".localized }) {
            self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: answer.rawValue)
            self.selfServiceAdDetails.inspectionFileName = ""
            self.selfServiceAdDetails.inspectionFile = ""
            self.selfServiceAdDetails.uploadedInspectionFile = nil
            self.tableView.reloadData()
        }
        
        self.userSelectedNewData.removeValue(forKey: SelfSellingScreensType.uploadInspectionReport.rawValue)
    }
    
    func sendNewUploadedImages(images: [UploadImageItemModel]) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Car photos".localized }) {
            let photosWord = images.count > 1 ? "photos".localized : "photo".localized
            self.carDetailsList[index].1 = "\(images.count) " + photosWord
            self.selfServiceAdDetails.lstImages?.removeAll()
            self.selfServiceAdDetails.uploadedImages = images
            self.tableView.reloadData()
        }
        
        self.uploadedImages.removeAll()
        self.uploadedImages = images
    }
}

extension EditSelfServiceCarAfterCheckoutVC {
    @objc
    private func getSelectedBoyPaintedBackFromPaintVisualScreen(_ notification: NSNotification) {
        if let selectedParts = notification.object as? [Int] {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Paint condition".localized }) {
                self.carDetailsList[index].1 = self.myCarDetailsVM.getPaintConditionName(by: 1004, from: self.paintsArray)
                self.selfServiceAdDetails.paintLocations = selectedParts
                self.selfServiceAdDetails.paintId = 1004
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.paintVisual.rawValue] = [
                SelectedSelfSellingData(name: "PaintLocations", id: 0, ids: selectedParts)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.paintCondition.rawValue] = [
                SelectedSelfSellingData(name: "PaintId", id: 1004)
            ]
        }
    }
    
    @objc
    private func getSelectedDamagePartsBackFromPaintVisualScreen(_ notification: NSNotification) {
        if let damageBodyParts = notification.object as? [CustomBodyPart] {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Body panels issues".localized }) {
                self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: AnyDamagesAnswer.yes.rawValue)
                let damageLocations = damageBodyParts.map {
                    CarBodyDamageLocations(damageId: $0.damageId, damageLocation: $0.damageLocation.rawValue, note: $0.note)
                }
                self.selfServiceAdDetails.carBodyDamageLocations = damageLocations
                self.selfServiceAdDetails.carBodyDamageAnswer = AnyDamagesAnswer.yes.rawValue
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.bodyDamageVisual.rawValue] = [
                SelectedSelfSellingData(name: "CarBodyDamageLocations", id: 0, customBodyParts: damageBodyParts)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.anyBodyDamage.rawValue] = [
                SelectedSelfSellingData(name: "CarBodyDamageAnswer", id: AnyDamagesAnswer.yes.rawValue)
            ]
        }
    }
    
    @objc
    private func getSelectedDashLightIssues(_ notification: NSNotification) {
        if let dashLightIssues = notification.object as? [Int] {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Dashboard warning lights".localized }) {
                self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: AnyDashboardIssues.yes.rawValue)
                self.selfServiceAdDetails.dashboardIssuesIds = dashLightIssues
                self.selfServiceAdDetails.dashboardIssuesAnswer = AnyDashboardIssues.yes.rawValue
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.dashLightIssues.rawValue] = [
                SelectedSelfSellingData(name: "DashboardIssuesIds", id: 0, ids: dashLightIssues)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.anyDashLightIssues.rawValue] = [
                SelectedSelfSellingData(name: "DashboardIssuesAnswer", id: AnyDashboardIssues.yes.rawValue)
            ]
        }
    }
    
    @objc
    private func getSelectedChassieIssues(_ notification: NSNotification) {
        if let chassieIssues = notification.object as? [Int] {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Chassis issues".localized }) {
                self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: AnyChassieIssues.yes.rawValue)
                self.selfServiceAdDetails.chassisIssuesIds = chassieIssues
                self.selfServiceAdDetails.chassisIssuesAnswer = AnyChassieIssues.yes.rawValue
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.chassieIssues.rawValue] = [
                SelectedSelfSellingData(name: "ChassisIssuesIds", id: 0, ids: chassieIssues)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.anyChassieIssues.rawValue] = [
                SelectedSelfSellingData(name: "ChassisIssuesAnswer", id: AnyChassieIssues.yes.rawValue)
            ]
        }
    }
    
    @objc
    private func getSelectedMechanicalIssues(_ notification: NSNotification) {
        if let mechanicalIssues = notification.object as? [Int] {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Mechanical issues".localized }) {
                self.carDetailsList[index].1 = self.myCarDetailsVM.getAnyAnswerText(for: AnyMechanicalIssues.yes.rawValue)
                self.selfServiceAdDetails.mechanicalIssuesIds = mechanicalIssues
                self.selfServiceAdDetails.mechanicalIssuesAnswer = AnyMechanicalIssues.yes.rawValue
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.mechanicalIssues.rawValue] = [
                SelectedSelfSellingData(name: "MechanicalIssuesIds", id: 0, ids: mechanicalIssues)
            ]
            
            self.userSelectedNewData[SelfSellingScreensType.anyMechanicalIssues.rawValue] = [
                SelectedSelfSellingData(name: "MechanicalIssuesAnswer", id: AnyMechanicalIssues.yes.rawValue)
            ]
        }
    }
    
    @objc
    private func getInspectionReportFile(_ notification: NSNotification) {
        if let uploadedFile = notification.object as? File {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Inspection report".localized }) {
                self.carDetailsList[index].1 = uploadedFile.name != "" ? (uploadedFile.name ?? "") : ("No".localized)
                self.selfServiceAdDetails.uploadedInspectionFile = uploadedFile
                self.selfServiceAdDetails.inspectionFileName = uploadedFile.name ?? ""
                self.selfServiceAdDetails.inspectionFile = ""
                self.tableView.reloadData()
            }
            
            self.userSelectedNewData[SelfSellingScreensType.uploadInspectionReport.rawValue] = [
                SelectedSelfSellingData(name: "InspectionReportFile", id: 0, reportFile: uploadedFile)
            ]
        }
    }
    
    @objc
    private func getMakeChange(_ notification: NSNotification) {
        if let makeObject = notification.object as? DataAttribute {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Make".localized }) {
                self.carDetailsList[index].1 = makeObject.name ?? ""
                self.selfServiceAdDetails.brandId = makeObject.id
                self.selfServiceAdDetails.brandName = makeObject.name
                self.tableView.reloadData()
            }
            
            self.userData["0"] = DataAttribute(name: makeObject.name, id: makeObject.id, key: "Make".localized, dataKey: "BrandID")
            self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
        }
    }
        
    @objc
    private func getModelChange(_ notification: NSNotification) {
        if let modelObject = notification.object as? DataAttribute {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Model".localized }) {
                self.carDetailsList[index].1 = modelObject.name ?? ""
                self.selfServiceAdDetails.modelId = modelObject.id
                self.selfServiceAdDetails.modelName = modelObject.name
                self.tableView.reloadData()
            }
            
            self.userData["1"] = DataAttribute(name: modelObject.name, id: modelObject.id, key: "Model".localized, dataKey: "ModelID")
            self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
        }
    }
    
    @objc
    private func getTrimChange(_ notification: NSNotification) {
        if let trimObject = notification.object as? DataAttribute {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Trim".localized }) {
                self.carDetailsList[index].1 = trimObject.name ?? ""
                self.selfServiceAdDetails.trimId = trimObject.id
                self.selfServiceAdDetails.trimName = trimObject.name
                self.isTrim = true
                self.tableView.reloadData()
            } else {
                self.selfServiceAdDetails.trimId = trimObject.id
                self.selfServiceAdDetails.trimName = trimObject.name
                let trimRow: (String, Any) = ("Trim".localized, self.selfServiceAdDetails.trimName ?? "")
                self.carDetailsList.insert(trimRow, at: 2)
                self.isTrim = true
                self.tableView.reloadData()
            }
            
            self.userData["2"] = DataAttribute(name: trimObject.name, id: trimObject.id, key: "Trim".localized, dataKey: "TrimID")
            self.userData["14"] = self.userData["13"] as? DataAttribute
            self.userData.removeValue(forKey: "13")
            self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
        }
    }
    
    @objc
    private func removeTrimData(_ notification: NSNotification) {
        if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Trim".localized }) {
            self.carDetailsList.remove(at: index)
            self.selfServiceAdDetails.trimId = nil
            self.selfServiceAdDetails.trimName = nil
            self.isTrim = false
            self.tableView.reloadData()
        }
        
        self.userData["3"] = self.userData["4"] as? DataAttribute
        self.userData["4"] = self.userData["5"] as? DataAttribute
        self.userData["13"] = self.userData["14"] as? DataAttribute
        self.userData.removeValue(forKey: "2")
        self.userData.removeValue(forKey: "5")
        self.userData.removeValue(forKey: "14")
        self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
    }
    
    @objc
    private func getYearChange(_ notification: NSNotification) {
        if let yearObject = notification.object as? DataAttribute {
            if let index = self.carDetailsList.firstIndex(where: { $0.0 == "Year".localized }) {
                self.carDetailsList[index].1 = yearObject.id ?? 0
                self.selfServiceAdDetails.makeYear = yearObject.id
                self.tableView.reloadData()
            }
            
            self.userData[self.isTrim ? "3" : "2"] = DataAttribute(name: yearObject.name, id: yearObject.id, key: "Year".localized, dataKey: "Year")
            self.userData[isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
        }
    }
}

extension EditSelfServiceCarAfterCheckoutVC: SYCChooseAnotherPackageSheetVCProtocol {
    func chooseAnotherPackage() {
        if let navigationController = self.navigationController {
            for vc in navigationController.viewControllers {
                if let packagesViewController = vc as? SYCProcessPackagesVC {
                    packagesViewController.updateUserData(modifiedUserData: self.userData, adIdToPostApi: self.adIdToPostApi)
                    packagesViewController.setLstPackagesUpdatedFromEditDetailsScreen(newPackagesData: self.newPackageData, alreadyCalledApi: true)
                    navigationController.popToViewController(packagesViewController, animated: true)
                    break
                }
            }
        }
    }
}
