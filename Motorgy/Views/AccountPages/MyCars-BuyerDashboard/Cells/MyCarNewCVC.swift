//
//  MyCarNewCVC.swift
//  Motorgy
//
//  Created by <PERSON><PERSON> on 27/06/2021.
//  Copyright 2021 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import SwiftUI

class MyCarNewCVC: UICollectionViewCell {

    // MARK: - Outlets and Values
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var carImage: UIImageView!
    @IBOutlet weak var carTitleLbl: labelLocalization!
    @IBOutlet weak var yearLbl: labelLocalization!
    @IBOutlet weak var kmLbl: labelLocalization!
    @IBOutlet weak var imageStatus: UIImageView!
    @IBOutlet weak var statusLbl: labelLocalization!
    @IBOutlet weak var carImageViewContainer: UIView!
    @IBOutlet weak var leadsCountView: LeadCountView!
    @IBOutlet weak var expiredView: UIView!
    @IBOutlet weak var expiredInLabel: labelLocalization!
    @IBOutlet weak var expiresInDaysView: UI<PERSON>ie<PERSON>!
    @IBOutlet weak var expiresInLabel: labelLocalization!
    @IBOutlet weak var expiryDaysLabel: labelLocalization!
    @IBOutlet weak var repostButton: buttonLocalization!
    @IBOutlet weak var statusView: UIView!
    @IBOutlet weak var expiredDividedLineView: UIView!
    @IBOutlet weak var statusDividerView: UIView!
    @IBOutlet weak var headerViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var expiredViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var stackView: UIStackView!
    @IBOutlet weak var consumerPAckageTypeView: UIView!
    @IBOutlet weak var consumerPackageTypeLabel: labelLocalization!
    @IBOutlet weak var boostBadgeView: UIView!
    @IBOutlet weak var boostBadgeLabel: labelLocalization!

    private var hostingController: UIHostingController<CarImageView>?
    public var openLeadsScreenCallBackFromMyCars: ((Car?) -> Void)?
    public var repostCarButtonTapped: ((Int) -> Void)?
    private var carId: Int = 0
    private var car: Car?
    private var dashedBorderLayer: CAShapeLayer?

    // MARK: - prepareForReuse
    override func prepareForReuse() {
        super.prepareForReuse()

        hostingController?.view.removeFromSuperview()
        hostingController = nil
        // ALSO clear old border
        dashedBorderLayer?.removeFromSuperlayer()
        dashedBorderLayer = nil

        // Reset all tag-related styling to prevent reuse issues
        resetTagStyling()
    }

    private func resetTagStyling() {
        // Reset consumer package type view
        consumerPAckageTypeView.isHidden = true
        consumerPAckageTypeView.backgroundColor = .clear
        consumerPAckageTypeView.layer.borderColor = UIColor.clear.cgColor
        consumerPAckageTypeView.layer.borderWidth = 0
        consumerPAckageTypeView.layer.cornerRadius = 0
        consumerPackageTypeLabel.text = ""
        consumerPackageTypeLabel.textColor = .clear

        // Reset boost badge view
        boostBadgeView.isHidden = true
        boostBadgeView.backgroundColor = .clear
        boostBadgeView.cornerRadius = 0
        boostBadgeLabel.text = ""
        boostBadgeLabel.textColor = .clear

        // Remove any existing dashed border layers
        consumerPAckageTypeView.layer.sublayers?.removeAll { $0.name == "dashedBorder" }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        backgroundColor = .clear

        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
        containerView.layer.shadowRadius = 8
        containerView.layer.masksToBounds = false

        // Only handle border drawing here, styling is done in configureCell
        updateConsumerPackageBorder()
    }

    private func updateConsumerPackageBorder() {
        // Remove any existing border
        dashedBorderLayer?.removeFromSuperlayer()

        // Only add border if this is a self-service package (type 1) and view is visible
        if let type = car?.consumerPackageType, type == 1, !consumerPAckageTypeView.isHidden {
            consumerPAckageTypeView.layer.borderColor = Colors.slateColor.cgColor
            consumerPAckageTypeView.layer.borderWidth = 0.8
            consumerPAckageTypeView.layer.cornerRadius = 12
        }
    }

    // MARK: - configureCell
    public func configureCell(car: Car?) {
        self.car = car
        self.carId = car?.iD ?? 0
        self.carTitleLbl.text = car?.titleWithoutYear ?? ""
        self.yearLbl.text = "\(car?.year ?? 0)"
        self.statusLbl.text = car?.statusTitle ?? ""
        self.kmLbl.setMileage(mileage: car?.mileageName)
        self.carImage.isHidden = true
        self.carImageViewContainer.isHidden = false
        self.imageStatus.loadImageFromUrl(imgUrl: car?.progressImage ?? "")

        let carImageView = CarImageView(imageUrl: car?.lstImages?.first ?? "")
        hostingController = UIHostingController(rootView: carImageView)

        if let hostingController = hostingController {
            hostingController.view.translatesAutoresizingMaskIntoConstraints = false
            carImageViewContainer.addSubview(hostingController.view)
            carImageViewContainer.cornerRadius = 6
            carImageViewContainer.clipsToBounds = true

            NSLayoutConstraint.activate([
                hostingController.view.leadingAnchor.constraint(equalTo: carImageViewContainer.leadingAnchor),
                hostingController.view.trailingAnchor.constraint(equalTo: carImageViewContainer.trailingAnchor),
                hostingController.view.topAnchor.constraint(equalTo: carImageViewContainer.topAnchor),
                hostingController.view.bottomAnchor.constraint(equalTo: carImageViewContainer.bottomAnchor)
            ])
        }

        self.leadsCountView.configureViewWith(viewType: .myCars, leadsCount: car?.buyerLeadsCount ?? 0)

        self.leadsCountView.openLeadsScreenCallBackFromMyCars = { [weak self] in
            self?.openLeadsScreenCallBackFromMyCars?(car)
        }

        self.expiresInLabel.textColor = Colors.slateColor
        self.expiresInLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12)
        self.expiresInLabel.text = "Ad expires in ".localized

        self.expiryDaysLabel.textColor = Colors.slateColor
        self.expiryDaysLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: 12)

        var expiryDaysCount = 0
        var expiryHoursCount = 0

        if let expiryDate = getDateObjectManuallyWhenDateFormatIsSSSSSS(dateString: car?.expiredDate ?? "") {
            let (days, hours) = daysAndHoursBetween(Date(), expiryDate)
            expiryDaysCount = days
            expiryHoursCount = hours

            var dateText = ""
            if expiryDaysCount == 0 {
                if LanguageHelper.isEnglish {
                    dateText = "\(expiryHoursCount) \(expiryHoursCount == 1 ? "hour" : "hours")"
                } else {
                    dateText = "\(expiryHoursCount) \(expiryHoursCount == 1 ? "ساعة" : "ساعات")"
                }
            } else {
                if LanguageHelper.isEnglish {
                    dateText = "\(expiryDaysCount) \(expiryDaysCount == 1 ? "day" : "days")" + ", ".localized + "\(expiryHoursCount) \(expiryHoursCount == 1 ? "hour" : "hours")"
                } else {
                    dateText = "\(expiryDaysCount) \(expiryDaysCount == 1 ? "يوم" : "آيام")" + ", ".localized + "\(expiryHoursCount) \(expiryHoursCount == 1 ? "ساعة" : "ساعات")"
                }
            }
            self.expiryDaysLabel.text = dateText
        } else {
            expiryDaysCount = 0
            expiryHoursCount = 0
            self.expiryDaysLabel.text = ""
        }

        let attributes1: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12) ?? UIFont.systemFont(ofSize: 12),
            .foregroundColor: Colors.slateColor
        ]
        let part1 = NSMutableAttributedString(string: "Ad expired in ".localized, attributes: attributes1)

        let expiredDateText = convertDateManually(car?.expiredDate ?? "", showWithoutTime: true).arToEnDigits
        let attributes2: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: 12) ?? UIFont.boldSystemFont(ofSize: 12),
            .foregroundColor: Colors.grapefruitColor
        ]
        let part2 = NSAttributedString(string: expiredDateText, attributes: attributes2)

        part1.append(part2)

        self.expiredInLabel.attributedText = part1
        self.expiredInLabel.numberOfLines = 2

        self.repostButton.setTitle("Repost".localized, for: .normal)
        self.repostButton.setTitleColor(.white, for: .normal)
        self.repostButton.tintColor = Colors.shamrockColor
        self.repostButton.setAttributedTitle(NSAttributedString(string: "Repost".localized, attributes: [.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!]), for: .normal)
        self.repostButton.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(repostButtonTapped)))

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let hasBuyerLeads = car?.buyerLeadsCount ?? 0 > 0
            let isExpired = car?.isExpired ?? false
            let offerStatus = car?.offerStatus == 11

            self.leadsCountView.isHidden = !hasBuyerLeads

            if offerStatus {
                self.stackView.spacing = 12

                if isExpired || (expiryDaysCount == 0 && expiryHoursCount == 0) || car?.isParkedLot ?? false {
                    self.expiresInDaysView.isHidden = true
                    self.expiredDividedLineView.isHidden = false
                } else {
                    self.expiresInDaysView.isHidden = false
                    self.expiredDividedLineView.isHidden = true
                }

                if isExpired || (expiryDaysCount == 0 && expiryHoursCount == 0) {
                    self.leadsCountView.isHidden = true
                    self.statusView.isHidden = true
                    self.expiredView.isHidden = false
                    self.expiredDividedLineView.isHidden = false
                } else {
                    self.leadsCountView.isHidden = !hasBuyerLeads
                    self.statusView.isHidden = false
                    self.expiredView.isHidden = true
                    self.expiredDividedLineView.isHidden = true
                }
            } else {
                self.stackView.spacing = 3

                self.expiresInDaysView.isHidden = true
                self.statusView.isHidden = false
                self.leadsCountView.isHidden = !hasBuyerLeads
                self.statusDividerView.isHidden = true
                self.expiredView.isHidden = true
            }

            self.headerViewHeightConstraint.constant = LanguageHelper.isEnglish ? 67 : 74
            self.expiredViewHeightConstraint.constant = 55
        }

        let isSelfService = car?.adMainType == 5
        expiredView.isHidden = !isSelfService

        // Configure boost badge
        setupBoostBadge()

        // Configure consumer package type badge
        setupConsumerBadgeType()
    }

    private func setupBoostBadge() {
        if let _ = car?.isPremium, let boostPackageName = car?.boostPackageName, boostPackageName != "", boostPackageName != "null" {
            self.boostBadgeView.isHidden = false
            self.boostBadgeView.backgroundColor = UIColor.hexStringToUIColor(hex: "#FFF3CD")
            self.boostBadgeView.cornerRadius = 12

            self.boostBadgeLabel.textColor = .black
            self.boostBadgeLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12)
            self.boostBadgeLabel.text = boostPackageName
        } else {
            self.boostBadgeView.isHidden = true
        }
    }

    @objc
    private func repostButtonTapped() {
        self.repostCarButtonTapped?(self.carId)
    }

    private func setupConsumerBadgeType() {
        guard let consumerPackageType = car?.consumerPackageType else {
            consumerPAckageTypeView.isHidden = true
            return
        }

        consumerPAckageTypeView.isHidden = false
        consumerPAckageTypeView.layer.cornerRadius = 12
        consumerPAckageTypeView.layer.masksToBounds = true

        consumerPackageTypeLabel.textColor = .black
        consumerPackageTypeLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12)

        switch consumerPackageType {
        case 1:
            consumerPAckageTypeView.backgroundColor = .white
            consumerPackageTypeLabel.text = LanguageHelper.isEnglish ? "Self Service" : "الخدمة الذاتية"

        case 2:
            consumerPAckageTypeView.backgroundColor = UIColor.hexStringToUIColor(hex: "#E6F7ED")
            consumerPackageTypeLabel.text = "Concierge".capitalized.localized

        case 3:
            consumerPAckageTypeView.backgroundColor = UIColor.hexStringToUIColor(hex: "#EAF5F8")
            consumerPackageTypeLabel.text = self.car?.packageName ?? ""

        default:
            consumerPAckageTypeView.isHidden = true
            consumerPAckageTypeView.backgroundColor = .clear
            consumerPackageTypeLabel.text = ""
        }

        // Trigger layout update for border drawing
        setNeedsLayout()
    }

}

struct CarImageView: View {
    var imageUrl: String

    var placeholderImage: some View {
        Image(uiImage: UIImage(named: "no-image")!)
            .resizable()
            .scaledToFill()
    }

    var body: some View {
        if #available(iOS 15.0, *) {
            AsyncImage(url: URL(string: imageUrl)) { phase in
                if let image = phase.image {
                    image
                        .resizable()
                        .scaledToFill()
                } else if phase.error != nil {
                    placeholderImage
                } else {
                    placeholderImage
                }
            }
            .frame(width: 80, height: 50)
        }
    }
}
