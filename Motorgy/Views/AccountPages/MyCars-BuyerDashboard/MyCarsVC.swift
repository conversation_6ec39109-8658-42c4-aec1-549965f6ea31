//
//  MyCarsVC.swift
//  Motorgy
//
//  Created by ahmed ezz on 4/12/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit
import RxSwift
import FirebaseAnalytics
import FBSDKCoreKit
import SwiftUI

class MyCarsVC: BaseVC, OnLoadMore, OnDismiss {
    
    // MARK: - Outlets and Values
    @IBOutlet weak private var dataVw: UIView!
    @IBOutlet weak private var emptyVw: UIView!
    @IBOutlet weak private var carsCV: UICollectionView!
    @IBOutlet weak var userActivityVw: UIView!
    @IBOutlet weak var userActivityTable: UITableView!
    @IBOutlet weak var emptyActivityView: UIView!
    @IBOutlet weak var titleActivityEmptyLbl: UILabel!
    @IBOutlet weak var desActivityEmptyLbl: UILabel!
    @IBOutlet weak var browseBtn: UIButton!
    @IBOutlet weak var microDealerHeaderView: UIView!
    @IBOutlet weak var carFiltersView: UIView!
    @IBOutlet weak var listCarCustomView: SellingProcessCustomButtonView!
    @IBOutlet weak var filtersTopConstraint: NSLayoutConstraint! // 170
    @IBOutlet weak var microDealerHeaderViewHeight: NSLayoutConstraint! // 170
    @IBOutlet weak var listCarCustomButtonView: SellingProcessCustomButtonView!
    @IBOutlet weak var filtersViewHeight: NSLayoutConstraint!
    @IBOutlet weak var collectionViewTopConstraint: NSLayoutConstraint! // 266
    @IBOutlet weak var emptyIconTopConstraint: NSLayoutConstraint! // 266
    
    private var myCarsCVHandler:MyCarsCVHandler!
    private let myCarsVM = MyCarsViewModel()
    private let myCarDetailsVM = MyCarDetailsViewModel()
    private let getLandingSellCarVM = LanddingSellCarVM()
    private var pageId = 1, pageIDActivity = 1
    private var numberOfSection: Int = 0
    private var Book_a_home_demo = 1,
                Book_test_drive = 2,
                General_inquiry = 3,
                Book_car_viewing = 15,
                Make_Offer = 5,
                Finance_request = 6
    private var isSelling: Bool = false
    private var isBuying: Bool = false
    private var userData = [String : Any]()
    private var microDealerHeaderHostingController: MicroDealerHeaderHostingController?
    private var carFiltersHostingController: CarFiltersHostingController?
    private var carsFilterId = 0
    private var shouldShowFilters = false
    private var microDealerViewModel: MicroDealerViewModel?
    
    // MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupView()
    }
    
    // MARK: - setupView
    func setupView () {
        self.titleActivityEmptyLbl.text = "You're not buying any cars yet.".localized
        self.desActivityEmptyLbl.text = "Buy your next car from a wide selection of pre-inspected cars 100% guaranteed by experts.".localized
        self.browseBtn.setTitle("Browse Cars".localized, for: .normal)
        
        emptyActivityView.isHidden = true
        dataVw.isHidden = true
        emptyVw.isHidden = true
        
        self.pageId = 1
        
        myCarsCVHandler = MyCarsCVHandler(cv: carsCV, controller: self, onLoadMore: self)
        
        myCarsCVHandler.openLeadsScreenCallBackFromMyCars = { [weak self] car in
            self?.openBuyerLeadsScreen(car: car)
        }
        
        myCarsCVHandler.repostCarButtonTapped = { [weak self] carId in
            self?.repostExpiredCar(carId: carId)
        }
        
        if #available(iOS 15.0, *) {
            userActivityTable.sectionHeaderTopPadding = 16
        }
        
        self.listCarCustomButtonView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        
        self.listCarCustomButtonView.configureContinue(
            buttonTitle: LanguageHelper.isEnglish ? "List new car" : "أضف سيارة جديدة",
            backgroundColor: UIColor.hexStringToUIColor(hex: "#E6F2FF"),
            textColor: UIColor.hexStringToUIColor(hex: "#0078FF")
        ) { [weak self] in
            self?.checkForActiveBundlesBeforeListingCar()
        }
        
        self.configureMicroDealerUI()
        self.setupMicroDealerHeaderView()
        self.setupCarFiltersView()
    }
    
    private func setupMicroDealerHeaderView() {
        let bundles = self.myCarsVM.getMyCarsResult()?.bundles ?? []
        
        microDealerHeaderHostingController = MicroDealerHeaderHostingController(
            bundles: bundles,
            onBuyBundleTapped: { [weak self] in
                self?.navigateToBuyBundle()
            },
            onListCarTapped: { [weak self] connectedBundle in
                self?.microDealerListCarFlowFrom(connectedBundle: connectedBundle)
            },
            onInfoIconTapped: { [weak self] bundle in
                self?.showInfoPopup(bundle: bundle)
            },
            onRenewTapped: { [weak self] bundle in
				self?.navigateToRenewBundle(bundle: bundle)
    		}
        )
        
        guard let hostingController = microDealerHeaderHostingController else { return }
        
        addChild(hostingController)
        microDealerHeaderView.addSubview(hostingController.view)
        
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: microDealerHeaderView.topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: microDealerHeaderView.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: microDealerHeaderView.trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: microDealerHeaderView.bottomAnchor)
        ])
        
        hostingController.didMove(toParent: self)
    }
    
    private func showInfoPopup(bundle: BundleModelMyCars) {
        DispatchQueue.main.async { [weak self] in
            let popupView = BundleInfoPopupView(bundle: bundle) { [weak self] in
                self?.dismiss(animated: true)
            }
            let hostingController = UIHostingController(rootView: popupView)
            hostingController.modalPresentationStyle = .overFullScreen
            hostingController.modalTransitionStyle = .crossDissolve
            hostingController.view.backgroundColor = .clear
            self?.present(hostingController, animated: true)
        }
    }
    
    private func setupCarFiltersView() {
        let filters = self.myCarsVM.getMyCarsResult()?.dashboardCarStatusFilters ?? []
        carFiltersHostingController = CarFiltersHostingController(filters: filters) { [weak self] selectedFilterId in
            self?.handleFilterSelection(selectedFilterId)
        }
        
        guard let hostingController = carFiltersHostingController else { return }
        
        addChild(hostingController)
        carFiltersView.addSubview(hostingController.view)
        
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: carFiltersView.topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: carFiltersView.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: carFiltersView.trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: carFiltersView.bottomAnchor)
        ])
        
        hostingController.didMove(toParent: self)
    }
    
    private func handleFilterSelection(_ filterId: Int?) {
        self.carsFilterId = filterId ?? 0
        self.pageId = 1
        self.getMyCars()
    }
    
    private func updateCarFiltersView() {
        let filters = self.myCarsVM.getMyCarsResult()?.dashboardCarStatusFilters ?? []
        carFiltersHostingController?.updateFilters(filters)
    }
    
    private func navigateToBuyBundle() {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        guard let navigationController = self.navigationController else { return }
        SellingMultipleRouter.build(from: navigationController)
    }
    
    private func navigateToRenewBundle(bundle: BundleModelMyCars) {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = true
        guard let navigationController = self.navigationController else { return }
		SellingMultipleRouter.moveToMicroDealerCheckoutForRenewBundle(from: navigationController, bundle: bundle, isRenewBundle: true)
    }
    
    private func updateMicroDealerHeaderView() {
        let bundles = self.myCarsVM.getMyCarsResult()?.bundles ?? []
        microDealerHeaderHostingController?.updateBundles(bundles)
    }
    
    private func configureMicroDealerUI() {
        DispatchQueue.main.async {
            if self.isSelling {
                self.carFiltersView.isHidden = !self.shouldShowFilters
                self.listCarCustomButtonView.isHidden = false
                
                if self.myCarsVM.getMyCarsResult()?.bundles?.count == 0 {
                    self.microDealerHeaderView.isHidden = true
                    self.microDealerHeaderViewHeight.constant = 0
                    self.filtersTopConstraint.constant = 0
                    self.collectionViewTopConstraint.constant = 96
                    self.emptyIconTopConstraint.constant = 36
                } else {
                    self.microDealerHeaderView.isHidden = false
                    self.microDealerHeaderViewHeight.constant = 200
                    self.filtersTopConstraint.constant = 224
                    self.collectionViewTopConstraint.constant = 272
                    self.emptyIconTopConstraint.constant = 266
                }
            } else {
                self.listCarCustomButtonView.isHidden = true
                self.microDealerHeaderView.isHidden = true
                self.carFiltersView.isHidden = true
                self.collectionViewTopConstraint.constant = 24
                self.emptyIconTopConstraint.constant = 36
                self.microDealerHeaderViewHeight.constant = 0
                self.filtersViewHeight.constant = 0
            }
        }
    }
    
    // MARK: - getMyCars
    private func getMyCars() {
        self.switchBetweenBuySell(isBuy: false)

        self.myCarsVM.getMyCarsData(pageId: self.pageId, carsFilterId: self.carsFilterId).bind{[weak self ] _ in
            // Set shouldShowFilters only on initial load (first page with no filter)
            if self?.pageId == 1 && self?.carsFilterId == 0 {
                self?.shouldShowFilters = (self?.myCarsVM.getMyCarsList().count ?? 0) > 0
            }
            
            if self?.myCarsVM.getMyCarsList().count ?? 0 > 0 {
                self?.emptyVw.isHidden = true
                self?.dataVw.isHidden = false
                self?.emptyActivityView.isHidden = true
                self?.userActivityVw.isHidden = true
                self?.myCarsCVHandler.showData(myCarsList:  self?.myCarsVM.getMyCarsList() ?? [])
            } else {
                self?.emptyVw.isHidden = false
                self?.dataVw.isHidden = true
                self?.emptyActivityView.isHidden = true
                self?.userActivityVw.isHidden = true
            }
            
            self?.updateMicroDealerHeaderView()
            self?.updateCarFiltersView()
            
            if self?.myCarsVM.getMyCarsResult()?.aPIStatus != 1 {
                self?.carFiltersView.isHidden = true
                self?.listCarCustomButtonView.isHidden = true
                self?.microDealerHeaderView.isHidden = true
                self?.microDealerHeaderViewHeight.constant = 0
                self?.filtersTopConstraint.constant = 0
                self?.collectionViewTopConstraint.constant = 24
                self?.listCarCustomButtonView.isHidden = true
                self?.emptyIconTopConstraint.constant = 36
            } else {
                self?.configureMicroDealerUI()
            }
        }.disposed(by: self.myCarsVM.getDisposeBag())
    }
    
    // MARK: - getUserActivity
    private func getUserActivity() {
        self.switchBetweenBuySell(isBuy: true)

        self.myCarsVM.getUserActivity(pageId: self.pageIDActivity).bind{[weak self] _ in
            if self?.myCarsVM.getMyActivityList().count ?? 0 > 0 {
                self?.numberOfSection = self?.myCarsVM.getMyActivity()?.buyerActivity?.count ?? 0
                self?.userActivityTable.reloadData()
                self?.emptyActivityView.isHidden = true
                self?.userActivityVw.isHidden = false
                self?.emptyVw.isHidden = true
                self?.dataVw.isHidden = true
            } else {
                self?.emptyActivityView.isHidden = false
                self?.userActivityVw.isHidden = true
                self?.emptyVw.isHidden = true
                self?.dataVw.isHidden = true
            }
        }.disposed(by: self.myCarsVM.getDisposeBag())
    }
    
    // MARK: - exploreAction
    @IBAction func exploreAction(_ sender: Any) {
        for vc in self.navigationController?.viewControllers ?? [] {
            if let tabBar = vc as? UITabBarController {
                self.navigationController?.popToViewController(tabBar, animated: false)
                tabBar.selectedIndex = 1
            }
        }
    }
    
    // MARK: - sellYourCar
    @IBAction func sellYourCar(_ sender: Any) {
        if let tabBar = self.navigationController?.viewControllers.first as? UITabBarController {
            #if DEVELOPMENT
            #else
            Analytics.logEvent("sell_car_start", parameters: ["trigger" : "empty_my_cars"])
//            AppEvents.shared.logEvent(AppEvents.Name.init(rawValue: "sell_car_start"), parameters: [AppEvents.ParameterName("trigger"): "empty_my_cars"])
            #endif
            self.navigationController?.popToViewController(of: UITabBarController.self, animated: false)

            trigger = "empty_my_cars"

            tabBar.selectedIndex = 2
        }
    }
    
    // MARK: - loadMore
    internal func loadMore() {
        if self.isSelling {
            if self.myCarsVM.getMyCarsResult()?.count ?? 0 > self.myCarsVM.getMyCarsList().count {
                self.pageId += 1
                self.getMyCars()
            } else if self.isBuying {
                if self.myCarsVM.getMyActivity()?.totalCount ?? 0 > self.myCarsVM.getMyActivityList().count {
                    self.pageIDActivity += 1
                    self.getUserActivity()
                }
            }
        }
    }
    
    func setupFlagsFromMyGarageScreen(isBuying: Bool, isSelling: Bool) {
        self.isBuying = isBuying
        self.isSelling = isSelling
    }
    
    // MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        
        if self.isBuying {
            #if DEVELOPMENT
            #else
            Analytics.logEvent("buying_mycars", parameters: [:])
            #endif
            getUserActivity()
        } else if self.isSelling {
            #if DEVELOPMENT
            #else
            Analytics.logEvent("selling_mycars", parameters: [:])
            #endif
            getMyCars()
        }

        #if DEVELOPMENT
        #else
        Analytics.logEvent("my_cars", parameters: [:])
        #endif
        
        self.navigationItem.title = "My cars".localized
        self.navigationItem.backButtonTitle = " "
        
//        if ConstantsValues.sharedInstance.fromAllActivity == false {
//            getMyCars()
//        } else if ConstantsValues.sharedInstance.fromAllActivity == true {
//            if pageIDActivity == 1 {
//                getUserActivity()
//            }
//            if self.myCarsVM.getMyActivity()?.totalCount ?? 0 > self.myCarsVM.getMyActivityList().count {
//                getUserActivity()
//            }
//        }
    }
    
    func refreshDataCars() {
        if ConstantsValues.sharedInstance.fromAllActivity == false {
            getMyCars()
        } else if ConstantsValues.sharedInstance.fromAllActivity == true {
            pageIDActivity = 1
            getUserActivity()
        }
    }
    
    // MARK: - viewWillDisappear
    override func viewWillDisappear(_ animated: Bool) {
        self.navigationItem.title = ""
        
        if self.isMovingFromParent {
            if #available(iOS 18.0, *) {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            } else if #available(iOS 17.0, *) {
                self.navigationController?.navigationBar.isHidden = true
            } else {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            }
        }
    }
    
    private func openBuyerLeadsScreen(car: Car?) {
        DispatchQueue.main.async {
            let buyersLeadsVC = self.getNextViewController(viewControllerClass: BuyersLeadsScreenVC.self, storyBoardName: "Account", identifier: "BuyersLeadsScreenVC") ?? BuyersLeadsScreenVC()
            buyersLeadsVC.configureScreen(
                carTitle: car?.titleWithoutYear ?? "",
                carPrice: car?.highestPrice ?? 0,
                adId: car?.iD ?? 0,
                carImageUrl: car?.lstImages?.first ?? ""
            )
            self.navigationController?.pushViewController(buyersLeadsVC, animated: true)
        }
    }
    
    private func repostExpiredCar(carId: Int) {
        self.myCarDetailsVM.resetDisposeBag()
        self.getLandingSellCarVM.resetDisposeBag()
        
        self.myCarDetailsVM.getSelfServiceCarById(adId: carId).bind { [weak self] editSelfServiceCarResponse in
            guard let self = self, let selfServiceAdDetails = editSelfServiceCarResponse?.selfServiceAdDetails else {
                return
            }
            
            var userData: [String: Any] = [:]
            let isTrim = selfServiceAdDetails.trimId != nil && selfServiceAdDetails.trimId != 0
            
            userData["0"] = DataAttribute(name: selfServiceAdDetails.brandName ?? "", id: selfServiceAdDetails.brandId ?? 0, key: "Make".localized, dataKey: "BrandID")
            userData["1"] = DataAttribute(name: selfServiceAdDetails.modelName ?? "", id: selfServiceAdDetails.modelId ?? 0, key: "Model".localized, dataKey: "ModelID")
            
            if isTrim {
                userData["2"] = DataAttribute(name: selfServiceAdDetails.trimName ?? "", id: selfServiceAdDetails.trimId ?? 0, key: "Trim".localized, dataKey: "TrimID")
            }
            
            userData[isTrim ? "3" : "2"] = DataAttribute(name: selfServiceAdDetails.makeYear?.description ?? "", id: selfServiceAdDetails.makeYear ?? 0, key: "Year".localized, dataKey: "Year")
            userData[isTrim ? "4" : "3"] = DataAttribute(name: selfServiceAdDetails.mileage?.withCommas() ?? "", id: Int(selfServiceAdDetails.mileage ?? 0), key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
            userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: Int(selfServiceAdDetails.estimatedPrice ?? 0), key: "Price".localized, dataKey: "EstimatedPrice")
            userData[isTrim ? "6" : "5"] = DataAttribute(name: "PackageId", id: selfServiceAdDetails.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
            
            self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
                guard let self = self else { return }
                
                let vc = self.getNextViewController(viewControllerClass: BoardingPackagesVC.self, storyBoardName: "SelfService", identifier: "BoardingPackagesVC") ?? BoardingPackagesVC()
                vc.configureData(
                    lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations ?? [],
                    userData: userData,
                    isTrim: isTrim,
                    lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
                )
                vc.setComingFromDashboardOrMyCarsScreenForRepostCar(comingFromDashboardOrMyCarsScreenForRepostCar: true, editSelfServiceCarResponse: editSelfServiceCarResponse, carId: carId)
                self.navigationController?.pushViewController(vc, animated: true)
            }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
            
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
    
    // MARK: - switchBetweenBuySell
    func switchBetweenBuySell(isBuy: Bool) {
        if isBuy {
            self.registerCells()
            self.userActivityTable.showsVerticalScrollIndicator = false
            self.userActivityTable.delegate = self
            self.userActivityTable.dataSource = self
            self.userActivityTable.rowHeight = UITableView.automaticDimension
            self.userActivityTable.reloadData()
        } else {
            self.userActivityVw.isHidden = true
            self.emptyActivityView.isHidden = true
            if self.myCarsVM.getMyCarsList().count > 0 {
                self.emptyVw.isHidden = true
                self.dataVw.isHidden = false
                self.myCarsCVHandler.showData(myCarsList:  self.myCarsVM.getMyCarsList())
            } else {
                self.dataVw.isHidden = true
            }
        }
    }
    
    // MARK: - registerCells
    func registerCells() {
        self.userActivityTable.register(UINib(nibName: "BuyerMainCarTVC", bundle: nil), forCellReuseIdentifier: "BuyerMainCarTVC")
        self.userActivityTable.register(UINib(nibName: "BuyerInfoTVC", bundle: nil), forCellReuseIdentifier: "BuyerInfoTVC")
        self.userActivityTable.register(UINib(nibName: "BuyerInfoMakeOfferTVC", bundle: nil), forCellReuseIdentifier: "BuyerInfoMakeOfferTVC")
        self.userActivityTable.register(UINib(nibName: "BuyerSoldTVC", bundle: nil), forCellReuseIdentifier: "BuyerSoldTVC")
        self.userActivityTable.register(UINib(nibName: "BuyerNotSoldTVC", bundle: nil), forCellReuseIdentifier: "BuyerNotSoldTVC")
    }
    
    func notifyMeAction (id: Int = 0) {
        self.myCarsVM.notifyMe(adID: id).bind{[weak self] _ in
            print("Notify me SUCESS")
            self?.refreshDataCars()
        }.disposed(by: self.myCarsVM.getDisposeBag())
    }
    
}

// MARK: - Extention for TableView Activities
extension MyCarsVC: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return self.myCarsVM.getMyActivityList().count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch indexPath.row {
        case 0:
            let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerMainCarTVC") as! BuyerMainCarTVC
            let buyerActivity = self.myCarsVM.getMyActivityList()[indexPath.section]
            cell.loadView(carObj: buyerActivity.oAdCarModelLink)
            return cell
        case 1:
            switch self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails?.activityType {
            case BuyerTypeEnum.MakeOffer.rawValue:
                
                if (self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails?.statusId == 2) {
                    let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerInfoTVC") as! BuyerInfoTVC
                    cell.loadView(obj: self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails, isDealer: self.myCarsVM.getMyActivityList()[indexPath.section].oAdCarModelLink?.isDealerCar, dealerName: self.myCarsVM.getMyActivityList()[indexPath.section].oAdCarModelLink?.dealerName)
                    return cell
                } else {
                    let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerInfoMakeOfferTVC") as! BuyerInfoMakeOfferTVC
                    cell.loadView(obj: self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails)
                    return cell
                }

            case BuyerTypeEnum.BookCarViewing.rawValue, BuyerTypeEnum.Finance.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerInfoTVC") as! BuyerInfoTVC
                cell.loadView(obj: self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails)
                return cell
                    
            case BuyerTypeEnum.Cancelled.rawValue, BuyerTypeEnum.CarCanceled.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerNotSoldTVC") as! BuyerNotSoldTVC
                cell.loadView(obj: self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails, isNotifyEnable: self.myCarsVM.getMyActivityList()[indexPath.section].isNotifyEnable ?? false, isDealer: self.myCarsVM.getMyActivityList()[indexPath.section].oAdCarModelLink?.isDealerCar ?? false)
                cell.onClickSelectNotifyMe = { [weak self] in
                    self?.notifyMeAction(id: self?.myCarsVM.getMyActivityList()[indexPath.section].adId ?? 0)
                }
                return cell
                    
            case BuyerTypeEnum.CarSold.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: "BuyerSoldTVC") as! BuyerSoldTVC
                cell.loadView(obj: self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails, isRate: self.myCarsVM.getMyActivityList()[indexPath.section].oReviewDetails?.isRate ?? false)
                cell.onClickSelectRate = { [weak self] in
                    let rateVC = self?.getNextViewController(viewControllerClass: RateServiceVC.self, storyBoardName: "Main", identifier: "RateServiceVC") ?? RateServiceVC()
                    rateVC.setData(controller: self ?? MyCarsVC(), oCheckReview: self?.myCarsVM.getMyActivityList()[indexPath.section].oReviewDetails ?? OCheckReview(), AdID: self?.myCarsVM.getMyActivityList()[indexPath.section].adId ?? 0, BD: true, fromBuyerDashboard: true)
                    rateVC.modalPresentationStyle = .custom
                    self?.present(rateVC, animated: true, completion: {})
                }
                return cell
                    
            default:
                return UITableViewCell()
            }
        default:
            return UITableViewCell()
        }

    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        switch self.myCarsVM.getMyActivityList()[indexPath.section].buyerActivityDetails?.activityType {
            case BuyerTypeEnum.MakeOffer.rawValue,
                BuyerTypeEnum.BookCarViewing.rawValue,
                BuyerTypeEnum.Finance.rawValue:
                let buyerCarDetailsVC = self.getNextViewController(viewControllerClass: BuyerCarDetailsVC.self, storyBoardName: "Account", identifier: "BuyerCarDetailsVC") ?? BuyerCarDetailsVC()
                buyerCarDetailsVC.setData(carID: self.myCarsVM.getMyActivityList()[indexPath.section].adId ?? 0)
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: true)
                } else if #available(iOS 17.0, *) {
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: true)
                }
                self.navigationController?.pushViewController(buyerCarDetailsVC, animated: true)
                
            case BuyerTypeEnum.CarSold.rawValue,
                BuyerTypeEnum.Cancelled.rawValue,
                BuyerTypeEnum.CarCanceled.rawValue:
                let buyerCarHistoeyVC = self.getNextViewController(viewControllerClass: BuyerHistoryVC.self, storyBoardName: "Account", identifier: "BuyerHistoryVC") ?? BuyerHistoryVC()
                buyerCarHistoeyVC.setData(carID: self.myCarsVM.getMyActivityList()[indexPath.section].adId ?? 0, type: .makeOffer, isSoldCancelled: true)
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: true)
                } else if #available(iOS 17.0, *) {
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: true)
                }
                self.navigationController?.pushViewController(buyerCarHistoeyVC, animated: true)
                
            default:
                break
        }
        
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
       
    // for section border and spaces
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView()
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 1
   }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        CGFloat.leastNormalMagnitude
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if (cell.responds(to: #selector(getter: UIView.tintColor))) {
            let cornerRadius: CGFloat = 8
            cell.backgroundColor = UIColor.clear
            let layer: CAShapeLayer  = CAShapeLayer()
            let pathRef: CGMutablePath  = CGMutablePath()
            let bounds: CGRect  = cell.bounds.insetBy(dx: 0, dy: 0)
            if (indexPath.row == 0 && indexPath.row == tableView.numberOfRows(inSection: indexPath.section)-1) {
                pathRef.__addRoundedRect(transform: nil, rect: bounds, cornerWidth: cornerRadius, cornerHeight: cornerRadius)
            } else if (indexPath.row == 0) {
                pathRef.move(to: CGPoint(x:bounds.minX,y:bounds.maxY))
                pathRef.addArc(tangent1End: CGPoint(x:bounds.minX,y:bounds.minY), tangent2End: CGPoint(x:bounds.midX,y:bounds.minY), radius: cornerRadius)

                pathRef.addArc(tangent1End: CGPoint(x:bounds.maxX,y:bounds.minY), tangent2End: CGPoint(x:bounds.maxX,y:bounds.midY), radius: cornerRadius)
                pathRef.addLine(to: CGPoint(x:bounds.maxX,y:bounds.maxY))
                //addLine = true;
            } else if (indexPath.row == tableView.numberOfRows(inSection: indexPath.section)-1) {

                pathRef.move(to: CGPoint(x:bounds.minX,y:bounds.minY))
                pathRef.addArc(tangent1End: CGPoint(x:bounds.minX,y:bounds.maxY), tangent2End: CGPoint(x:bounds.midX,y:bounds.maxY), radius: cornerRadius)

                pathRef.addArc(tangent1End: CGPoint(x:bounds.maxX,y:bounds.maxY), tangent2End: CGPoint(x:bounds.maxX,y:bounds.midY), radius: cornerRadius)
                pathRef.addLine(to: CGPoint(x:bounds.maxX,y:bounds.minY))

            } else {
                pathRef.addRect(bounds)
            }
            layer.path = pathRef
            layer.strokeColor = Colors.paleBlueColor.cgColor
            layer.lineWidth = 1
            layer.fillColor = UIColor(white: 1, alpha: 1.0).cgColor

            let testView: UIView = UIView(frame:bounds)
            testView.layer.insertSublayer(layer, at: 0)
            testView.backgroundColor = UIColor.clear
            testView.clipsToBounds = true
            cell.backgroundView = testView
        }

    }

    // load More Functions
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            scrolledToBottom(scrollView)
        }
    }
    
    // check if scroll view arrive to bottom
    private func scrolledToBottom(_ scrollview:UIScrollView) {
        let bottomEdge = scrollview.contentOffset.y + scrollview.frame.size.height
        if bottomEdge >= scrollview.contentSize.height {
            self.loadMore()
        }
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        scrolledToBottom(scrollView)
    }
}

extension MyCarsVC {
    private func checkForMobileNumberFirst() {
        let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
        validationVC.modalPresentationStyle = .custom
        self.present(validationVC, animated: true)
    }
    
    private func checkForActiveBundlesBeforeListingCar() {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                self.checkForMobileNumberFirst()
            } else {
                self.microDealerViewModel = MicroDealerViewModel(navigationController: self.navigationController)
                
                self.microDealerViewModel?.getActiveSubscriptionsForMicroDealer { [weak self] result in
                    DispatchQueue.main.async {
                        if result?.aPIStatus == 1 && result?.activeSubscriptions?.count ?? 0 > 0 {
                            self?.showConfirmProceedDialog()
                        } else {
                            self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
                        }
                    }
                }
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func showConfirmProceedDialog() {
        let dialogView = ConfirmProceedActiveBundleDialog(
            isPresented: .constant(true),
            onProceed: { [weak self] in
                self?.handleProceedAction()
            },
            onCancel: { [weak self] in
                self?.handleCancelAction()
            },
            onDismiss: { [weak self] in
                self?.handleDismissAction()
            }
        )
        
        let hostingController = UIHostingController(rootView: dialogView)
        hostingController.modalPresentationStyle = .overFullScreen
        hostingController.modalTransitionStyle = .crossDissolve
        hostingController.view.backgroundColor = .clear
        
        self.present(hostingController, animated: true)
    }
    
    private func handleProceedAction() {
        self.dismiss(animated: true) { [weak self] in
            self?.moveToBundleSelectionDialog(microDealerViewModel: self?.microDealerViewModel)
        }
    }
    
    private func handleCancelAction() {
        self.dismiss(animated: true) { [weak self] in
            self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
        }
    }
    
    private func handleDismissAction() {
        self.dismiss(animated: true)
    }
    
    private func moveToBundleSelectionDialog(microDealerViewModel: MicroDealerViewModel?) {
        let bundleSheet = BundleSelectionDialog(
            isPresented: .constant(true),
            bundles: microDealerViewModel?.activeSubscriptions ?? [],
            selectedBundleId: microDealerViewModel?.activeSubscriptions.first?.id ?? 0,
            onBundleSelected: { [weak self] selectedBundle in
                self?.dismiss(animated: true) {
                    ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = true
                    ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = selectedBundle
                    
                    self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
                }
            },
            onCancel: { [weak self] in
                self?.dismiss(animated: true)
            }, onDismiss: { [weak self] in
                self?.dismiss(animated: true)
            }
        )
        
        let hostingController = UIHostingController(rootView: bundleSheet)
        hostingController.modalPresentationStyle = .custom
        hostingController.view.backgroundColor = .clear
        self.present(hostingController, animated: true)
    }
    
    private func moveToCarDetails(isFirst: Bool, type: String?, data: [Any]?) {
        // Fetch landing data first to populate lstInspectionLocations and lstCityWithAreas
        self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
            DispatchQueue.main.async {
                let carDetailsVC = self?.getNextViewController(viewControllerClass: CarDetailsVC.self, storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
                carDetailsVC.setData(
                    brandModelResult: BrandModelResult.shared.self,
                    onDismiss: self,
                    isFirst: true,
                    type: BrandModelResult.shared.self?.type,
                    data: BrandModelResult.shared.self?.lstBrandModelYear,
                    userData: self?.userData,
                    serviceId: 0
                )
                carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)

                // Set the inspection locations and city areas data
                carDetailsVC.setInspectionAndPackagesData(
                    lstInspectionLocations: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
                    lstCityWithAreas: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
                    lstMasterPackages: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
                )

                self?.navigationController?.pushViewController(carDetailsVC, animated: true)
            }
        }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
    }
    
    private func microDealerListCarFlowFrom(connectedBundle: BundleModelMyCars) {
        DispatchQueue.main.async {
            if UserHelper.user.isLogin() {
                let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
                
                if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                    self.checkForMobileNumberFirst()
                } else {
                    ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = true
                    ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = connectedBundle
                    
					let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self, storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
					carDetailsVC.setData(
						brandModelResult: BrandModelResult.shared.self,
						onDismiss: self,
						isFirst: true,
						type: BrandModelResult.shared.self?.type,
						data: BrandModelResult.shared.self?.lstBrandModelYear,
						userData: self.userData,
						serviceId: 0
					)
					carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)
					carDetailsVC.setComingFromListCarMicroDealere(isComingFromMicroDealerListCar: true)
					carDetailsVC.goBackToMyCarsFromListCarFlowMicroDealer = {
						ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
						ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
					}
					self.navigationController?.pushViewController(carDetailsVC, animated: true)
                }
            } else {
                self.moveToLogin()
            }
        }
    }
}
