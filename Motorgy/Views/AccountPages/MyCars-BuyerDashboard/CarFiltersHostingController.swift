//
//  CarFiltersHostingController.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 15/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI
import UIKit

class CarFiltersHostingController: UIHostingController<CarFiltersView> {

    init(filters: [DashboardCarStatusFilters], selectedFilterId: Int = 0, onFilterTapped: @escaping (Int?) -> Void) {
        let carFiltersView = CarFiltersView(filters: filters, initialSelectedFilterId: selectedFilterId, onFilterTapped: onFilterTapped)
        super.init(rootView: carFiltersView)
    }

    func updateFilters(_ filters: [DashboardCarStatusFilters], selectedFilterId: Int = 0) {
        let carFiltersView = CarFiltersView(filters: filters, initialSelectedFilterId: selectedFilterId, onFilterTapped: rootView.onFilterTapped)
        rootView = carFiltersView
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
