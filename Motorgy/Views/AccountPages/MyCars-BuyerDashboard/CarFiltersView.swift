//
//  CarFiltersView.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 15/01/2025.
//  Copyright 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct CarFiltersView: View {
    let filters: [DashboardCarStatusFilters]
    let initialSelectedFilterId: Int
    @State private var selectedFilterId: Int = 0
    let onFilterTapped: (Int?) -> Void
    
    var body: some View {
        VStack(alignment: .center, spacing: 8) {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(filters, id: \.Id) { filter in
                        FilterTagView(
                            title: filter.Name ?? "",
                            isSelected: selectedFilterId == filter.Id,
                            onTapped: {
                                selectedFilterId = filter.Id ?? 0
                                onFilterTapped(filter.Id)
                            }
                        )
                    }
                }
                .padding(.horizontal, 16)
                
            }
            .frame(height: 30)
            
            Rectangle()
                .stroke(style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                .foregroundColor(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#EAECF0")))
                .frame(height: 1)
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 16)
				.padding(.top, 12)
        }
        .background(Color.white)
        .cornerRadius(12)
        .padding(.horizontal, 16)
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
        .onAppear {
            // Use the provided initial selected filter ID, or fallback to first filter
            if initialSelectedFilterId != 0 {
                selectedFilterId = initialSelectedFilterId
            } else if let firstFilter = filters.first {
                selectedFilterId = firstFilter.Id ?? 0
            }
        }
    }
}

struct FilterTagView: View {
    let title: String
    let isSelected: Bool
    let onTapped: () -> Void
    
    var body: some View {
        Button(action: onTapped) {
            Text(title)
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(isSelected ? .semibold : .regular)
                )
                .foregroundColor(isSelected ? .white : .init(uiColor: Colors.slateColor))
                .padding(.horizontal, 20)
                .frame(height: 30)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color(uiColor: Colors.bluishColor) : Color(uiColor: UIColor.hexStringToUIColor(hex: "#F2F4F7")))
                )
        }
    }
}
